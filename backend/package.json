{"name": "backend", "version": "1.3.0", "type": "commonjs", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js", "dev": "nodemon server.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "iconv-lite": "^0.6.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "multer": "^1.4.5-lts.2", "xlsx": "^0.18.5"}, "description": "", "devDependencies": {"axios": "^1.8.1"}}