const express = require('express');
const multer = require('multer');
const xlsx = require('xlsx');
const iconv = require('iconv-lite');
const Attendance = require('../models/Attendance');
const Employee = require('../models/Employee');
const AttendanceCalculator = require('../utils/AttendanceCalculator');
const fs = require('fs');
const path = require('path');
const router = express.Router();

const upload = multer({ dest: 'uploads/' });

// 上传并解析zkteco考勤文件
router.post('/upload', upload.single('attendance'), async (req, res) => {
  try {
    const filePath = req.file.path;
    
    // 尝试不同的编码方式读取文件
    let workbook;
    
    // 检查文件扩展名
    const fileExtension = path.extname(req.file.originalname).toLowerCase();
    
    if (fileExtension === '.csv') {
      // CSV文件特殊处理，支持多种编码
      const buffer = fs.readFileSync(filePath);
      let csvContent = null;
      
      // 尝试不同编码读取CSV
      const encodings = ['utf8', 'gbk', 'gb2312', 'latin1'];
      for (const encoding of encodings) {
        try {
          csvContent = iconv.decode(buffer, encoding);
          // 检查是否包含中文字符且没有乱码
          if (csvContent.includes('考勤') || csvContent.includes('姓名') || csvContent.includes('日期')) {
            console.log(`成功使用 ${encoding} 编码读取CSV文件`);
            break;
          }
        } catch (e) {
          continue;
        }
      }
      
      if (!csvContent) {
        csvContent = buffer.toString('utf8'); // 默认使用UTF-8
      }
      
      // 解析CSV内容
      workbook = xlsx.read(csvContent, { type: 'string' });
    } else {
      // Excel文件处理
      try {
        // 首先尝试默认编码
        workbook = xlsx.readFile(filePath);
      } catch (err) {
        // 如果失败，尝试使用GBK编码
        const buffer = fs.readFileSync(filePath);
        const decoded = iconv.decode(buffer, 'gbk');
        workbook = xlsx.read(decoded, { type: 'string' });
      }
    }
    
    const sheetName = workbook.SheetNames[0];
    const sheet = workbook.Sheets[sheetName];
    const jsonData = xlsx.utils.sheet_to_json(sheet, { defval: '' });

    console.log('解析到的数据列名:', Object.keys(jsonData[0] || {}));
    console.log('数据行数:', jsonData.length);
    console.log('前3行数据示例:', jsonData.slice(0, 3));

    // 智能列名识别 - 基于语义和模式匹配，支持完整的考勤文件列
    const columnPatterns = {
      serialNumber: {
        keywords: ['序号', '编号', '号', 'number'],
        patterns: [/序号/, /^编号$/, /^号$/, /number/i],
        priority: 1
      },
      employeeId: {
        keywords: ['考勤', '号码', '编号', '工号', '员工', 'ID', 'id', 'code', 'employeeid', 'employee'],
        patterns: [/考勤.*号/, /员工.*号/, /工号/, /编号/, /.*ID.*/, /.*code.*/i, /^employeeId$/i, /^employee$/i],
        priority: 1
      },
      customId: {
        keywords: ['自定义编号', '自定义', '编号'],
        patterns: [/自定义.*编号/, /自定义.*号/],
        priority: 2
      },
      name: {
        keywords: ['姓名', '员工', '名字', 'name'],
        patterns: [/姓名/, /员工.*名/, /.*name.*/i, /^name$/i],
        priority: 1
      },
      isSmartSchedule: {
        keywords: ['智能排班', '智能', '排班'],
        patterns: [/智能.*排班/, /是否.*智能/, /智能/],
        priority: 3
      },
      date: {
        keywords: ['日期', '时间', '年月日', 'date'],
        patterns: [/日期/, /.*date.*/i, /^date$/i, /\d{4}[-\/]\d{1,2}[-\/]\d{1,2}/],
        priority: 1
      },
      timeSlot: {
        keywords: ['时段', '班次', '对应时段'],
        patterns: [/对应.*时段/, /时段/, /班次/],
        priority: 3
      },
      workStartTime: {
        keywords: ['上班时间', '上班', '开始'],
        patterns: [/上班.*时/, /工作.*开始/, /开始.*时/],
        priority: 2
      },
      workEndTime: {
        keywords: ['下班时间', '下班', '结束'],
        patterns: [/下班.*时/, /工作.*结束/, /结束.*时/],
        priority: 2
      },
      checkInTime: {
        keywords: ['签到时间', '签到', '到岗', '进入', 'checkin', 'checkintime'],
        patterns: [/签到.*时/, /签到/, /到岗/, /进入/, /^checkInTime$/i, /^checkin$/i],
        priority: 2
      },
      checkOutTime: {
        keywords: ['签退时间', '签退', '离岗', '退出', 'checkout', 'checkouttime'],
        patterns: [/签退.*时/, /签退/, /离岗/, /退出/, /^checkOutTime$/i, /^checkout$/i],
        priority: 2
      },
      shouldAttend: {
        keywords: ['应到', '应出勤', 'shouldattend', 'should'],
        patterns: [/应.*到/, /应.*出/, /^shouldAttend$/i, /^should$/i],
        priority: 4
      },
      actualAttend: {
        keywords: ['实到', '实际出勤', 'actualattend', 'actual'],
        patterns: [/实.*到/, /实际.*出/, /^actualAttend$/i, /^actual$/i],
        priority: 4
      },
      lateTime: {
        keywords: ['迟到时间', '迟到', '晚到'],
        patterns: [/迟到.*时/, /迟到/, /晚到/],
        priority: 5
      },
      earlyTime: {
        keywords: ['早退时间', '早退', '提前'],
        patterns: [/早退.*时/, /早退/, /提前.*退/],
        priority: 5
      },
      isAbsent: {
        keywords: ['旷工', '缺勤', '是否旷工', 'isabsent', 'absent'],
        patterns: [/是否.*旷/, /旷工/, /缺勤/, /^isAbsent$/i, /^absent$/i],
        priority: 5
      },
      overtimeTime: {
        keywords: ['加班时间', '加班', '超时', 'overtime', 'overtimetime'],
        patterns: [/加班.*时/, /加班/, /超时/, /overtime/i, /^overtimeTime$/i],
        priority: 5
      },
      workTime: {
        keywords: ['工作时间', '工时', 'worktime', 'work'],
        patterns: [/工作.*时/, /工时/, /^workTime$/i, /^work$/i],
        priority: 5
      },
      exception: {
        keywords: ['例外情况', '例外', '异常'],
        patterns: [/例外.*情况/, /例外/, /异常/],
        priority: 6
      },
      shouldCheckIn: {
        keywords: ['应签到', '应到'],
        patterns: [/应.*签到/, /应.*到/],
        priority: 4
      },
      shouldCheckOut: {
        keywords: ['应签退', '应退'],
        patterns: [/应.*签退/, /应.*退/],
        priority: 4
      },
      department: {
        keywords: ['部门', '科室', '单位'],
        patterns: [/部门/, /科室/, /单位/],
        priority: 5
      },
      weekday: {
        keywords: ['平日', '工作日'],
        patterns: [/平日/, /工作日/],
        priority: 6
      },
      weekend: {
        keywords: ['周末', '休息日'],
        patterns: [/周末/, /休息日/],
        priority: 6
      },
      holiday: {
        keywords: ['节假日', '假日'],
        patterns: [/节假日/, /假日/],
        priority: 6
      },
      attendanceTime: {
        keywords: ['出勤时间', '出勤'],
        patterns: [/出勤.*时/, /出勤/],
        priority: 5
      },
      weekdayOvertime: {
        keywords: ['平日加班', '工作日加班'],
        patterns: [/平日.*加班/, /工作日.*加班/],
        priority: 6
      },
      weekendOvertime: {
        keywords: ['周末加班', '休息日加班'],
        patterns: [/周末.*加班/, /休息日.*加班/],
        priority: 6
      },
      holidayOvertime: {
        keywords: ['节假日加班', '假日加班'],
        patterns: [/节假日.*加班/, /假日.*加班/],
        priority: 6
      }
    };

    // 智能匹配函数
    function smartColumnMatch(columnName, patterns) {
      let score = 0;
      const normalizedName = columnName.toLowerCase();
      
      // 关键词匹配
      for (const keyword of patterns.keywords) {
        if (normalizedName.includes(keyword.toLowerCase())) {
          score += 10;
        }
      }
      
      // 正则模式匹配
      for (const pattern of patterns.patterns) {
        if (pattern.test(columnName)) {
          score += 15;
        }
      }
      
      // 优先级权重
      score = score / patterns.priority;
      
      return score;
    }

    // 智能识别列名映射
    function identifyColumns(columnNames) {
      const mapping = {};
      const usedColumns = new Set();
      
      // 为每个字段找到最佳匹配的列
      for (const [fieldName, patterns] of Object.entries(columnPatterns)) {
        let bestMatch = null;
        let bestScore = 0;
        
        for (const columnName of columnNames) {
          if (usedColumns.has(columnName)) continue;
          
          const score = smartColumnMatch(columnName, patterns);
          if (score > bestScore && score > 2) { // 降低最低匹配阈值
            bestScore = score;
            bestMatch = columnName;
          }
        }
        
        if (bestMatch) {
          mapping[fieldName] = bestMatch;
          usedColumns.add(bestMatch);
        }
      }
      
      return mapping;
    }

    // 智能识别列名
    const columnNames = Object.keys(jsonData[0] || {});
    console.log('Excel列名:', columnNames);
    
    // 使用智能识别算法
    let actualColumns = identifyColumns(columnNames);
    console.log('智能识别的列名映射:', actualColumns);
    
    // 手动添加英文列名的直接匹配
    const directMatches = {
      'shouldAttend': 'shouldAttend',
      'actualAttend': 'actualAttend', 
      'lateTime': 'lateTime',
      'earlyTime': 'earlyTime',
      'isAbsent': 'isAbsent',
      'workTime': 'workTime',
      'overtimeTime': 'overtimeTime'
    };
    
    for (const [field, columnName] of Object.entries(directMatches)) {
      if (columnNames.includes(columnName) && !actualColumns[field]) {
        actualColumns[field] = columnName;
      }
    }
    
    // 如果识别失败，尝试编码转换
    if (Object.keys(actualColumns).length < 3) {
      console.log('智能识别失败，尝试编码转换...');
      
      // 尝试多种编码转换
      const encodings = ['gbk', 'gb2312', 'utf8', 'latin1'];
      let bestMapping = {};
      let bestScore = 0;
      
      for (const encoding of encodings) {
        try {
          const convertedColumns = columnNames.map(name => {
            try {
              return iconv.decode(Buffer.from(name, 'binary'), encoding);
            } catch (e) {
              return name;
            }
          });
          
          const testMapping = identifyColumns(convertedColumns);
          const score = Object.keys(testMapping).length;
          
          if (score > bestScore) {
            bestScore = score;
            console.log(`使用 ${encoding} 编码转换后识别到更多列:`, testMapping);
            
            // 建立转换后列名与原始列名的映射关系
            const columnMapping = {};
            convertedColumns.forEach((converted, index) => {
              columnMapping[converted] = columnNames[index];
            });
            
            // 将字段映射到真实的Excel列名
            const finalMapping = {};
            for (const [field, convertedColumn] of Object.entries(testMapping)) {
              const originalColumn = columnMapping[convertedColumn];
              if (originalColumn) {
                finalMapping[field] = originalColumn;
              }
            }
            
            bestMapping = finalMapping;
            console.log('转换后的最终列名映射:', finalMapping);
          }
        } catch (e) {
          // 忽略编码转换错误
        }
      }
      
      if (bestScore > Object.keys(actualColumns).length) {
        actualColumns = bestMapping;
      }
    }
    
    console.log('最终识别的列名映射:', actualColumns);
    console.log('未匹配的Excel列名:', columnNames.filter(col => !Object.values(actualColumns).includes(col)));

    // 处理考勤数据
    const attendanceRecords = [];
    const processedCount = { total: 0, success: 0, skipped: 0 };

    for (const row of jsonData) {
      processedCount.total++;
      
      // 提取所有字段信息
      const serialNumber = row[actualColumns.serialNumber];
      const employeeId = row[actualColumns.employeeId];
      const customId = row[actualColumns.customId];
      const name = row[actualColumns.name];
      const isSmartSchedule = row[actualColumns.isSmartSchedule];
      const dateStr = row[actualColumns.date];
      const timeSlot = row[actualColumns.timeSlot];
      
      // 时间字段
      const workStartTime = row[actualColumns.workStartTime];
      const workEndTime = row[actualColumns.workEndTime];
      const checkInTime = row[actualColumns.checkInTime];
      const checkOutTime = row[actualColumns.checkOutTime];
      
      // 应到实到
      const shouldAttend = row[actualColumns.shouldAttend];
      const actualAttend = row[actualColumns.actualAttend];
      
      // 迟到早退
      const lateTime = row[actualColumns.lateTime];
      const earlyTime = row[actualColumns.earlyTime];
      
      // 工作时间
      const workTime = row[actualColumns.workTime];
      const attendanceTime = row[actualColumns.attendanceTime];
      const overtimeTime = row[actualColumns.overtimeTime];
      
      // 加班分类
      const weekdayOvertime = row[actualColumns.weekdayOvertime];
      const weekendOvertime = row[actualColumns.weekendOvertime];
      const holidayOvertime = row[actualColumns.holidayOvertime];
      
      // 其他字段
      const isAbsent = row[actualColumns.isAbsent];
      const exception = row[actualColumns.exception];
      const shouldCheckIn = row[actualColumns.shouldCheckIn];
      const shouldCheckOut = row[actualColumns.shouldCheckOut];
      const department = row[actualColumns.department];
      const weekday = row[actualColumns.weekday];
      const weekend = row[actualColumns.weekend];
      const holiday = row[actualColumns.holiday];
      
      // 兼容性：优先使用新字段，如果为空则使用旧的匹配逻辑
      const checkIn = checkInTime || workStartTime;
      const checkOut = checkOutTime || workEndTime;

      // 调试信息：显示前几行的解析结果
      if (processedCount.total <= 5) {
        console.log(`第${processedCount.total}行解析结果:`, {
          employeeId, name, dateStr, customId,
          workStartTime, workEndTime,
          checkInTime, checkOutTime,
          finalCheckIn: checkIn, finalCheckOut: checkOut,
          shouldAttend, actualAttend, isAbsent,
          lateTime, earlyTime, workTime, attendanceTime, overtimeTime
        });
      }

      // 验证必要字段
      if (!employeeId || !name || !dateStr) {
        if (processedCount.total <= 5) {
          console.log(`第${processedCount.total}行跳过原因:`, {
            missingEmployeeId: !employeeId,
            missingName: !name,
            missingDate: !dateStr
          });
        }
        processedCount.skipped++;
        continue;
      }

      // 解析日期
      let date;
      try {
        if (typeof dateStr === 'number') {
          // Excel日期序列号转换为日期
          // Excel的日期序列号是从1900年1月1日开始计算的天数
          const excelEpoch = new Date(1900, 0, 1);
          const millisecondsPerDay = 24 * 60 * 60 * 1000;
          // Excel有一个闰年bug，需要减去2天
          date = new Date(excelEpoch.getTime() + (dateStr - 2) * millisecondsPerDay);
        } else if (typeof dateStr === 'string') {
          date = new Date(dateStr);
        } else if (dateStr instanceof Date) {
          date = dateStr;
        } else {
          date = new Date(dateStr);
        }
        
        if (isNaN(date.getTime())) {
          console.log(`日期解析失败: ${dateStr} (${typeof dateStr})`);
          processedCount.skipped++;
          continue;
        }
        
        console.log(`日期解析成功: ${dateStr} -> ${date.toISOString().split('T')[0]}`);
      } catch (err) {
        console.log(`日期解析异常: ${dateStr} - ${err.message}`);
        processedCount.skipped++;
        continue;
      }

      // 处理时间格式 - 转换为完整的Date对象
      const formatTime = (timeStr, baseDate) => {
        if (!timeStr) return null;
        if (typeof timeStr === 'string') {
          // 处理各种时间格式
          const timeMatch = timeStr.match(/(\d{1,2}):(\d{2})(:\d{2})?/);
          if (timeMatch) {
            const hours = parseInt(timeMatch[1]);
            const minutes = parseInt(timeMatch[2]);
            const seconds = timeMatch[3] ? parseInt(timeMatch[3].substring(1)) : 0;
            
            // 创建完整的Date对象，使用当天的日期
            const fullDate = new Date(baseDate);
            fullDate.setHours(hours, minutes, seconds, 0);
            return fullDate;
          }
        }
        return null;
      };

      // 判断考勤状态
      let attendanceStatus = '正常';
      
      // 判断是否缺勤
      if (isAbsent === 'True' || isAbsent === '是' || isAbsent === '1' || isAbsent === true || 
          (shouldAttend === '1' && actualAttend !== '1')) {
        attendanceStatus = '缺勤';
      } else if (lateTime && lateTime !== '0' && lateTime !== '' && lateTime !== '00:00') {
        attendanceStatus = '迟到';
      } else if (earlyTime && earlyTime !== '0' && earlyTime !== '' && earlyTime !== '00:00') {
        attendanceStatus = '早退';
      } else if (!checkIn && !checkOut && shouldAttend === '1') {
        attendanceStatus = '缺勤';
      }

      // 计算工作时间（小时）
      let calculatedWorkHours = 0;
      let calculatedOvertimeHours = 0;
      
      if (workTime) {
        // 尝试从工作时间字符串中提取小时数
        const workTimeMatch = workTime.toString().match(/(\d+\.?\d*)/);
        if (workTimeMatch) {
          calculatedWorkHours = parseFloat(workTimeMatch[1]);
        }
      }
      
      if (overtimeTime) {
        // 尝试从加班时间字符串中提取小时数
        const overtimeMatch = overtimeTime.toString().match(/(\d+\.?\d*)/);
        if (overtimeMatch) {
          calculatedOvertimeHours = parseFloat(overtimeMatch[1]);
        }
      }

      // 确定工作日类型
      let workdayType = '平日';
      if (weekend && (weekend === '1' || weekend === 'True' || weekend === '是')) {
        workdayType = '周末';
      } else if (holiday && (holiday === '1' || holiday === 'True' || holiday === '是')) {
        workdayType = '节假日';
      }

      attendanceRecords.push({
        employeeId: null, // 稍后查找
        employeeCode: employeeId,
        name: name,
        customId: customId,
        date: date,
        
        // 时间相关字段
        workStartTime: formatTime(workStartTime, date),
        workEndTime: formatTime(workEndTime, date),
        checkInTime: formatTime(checkInTime, date),
        checkOutTime: formatTime(checkOutTime, date),
        
        // 应到实到
        shouldAttend: shouldAttend,
        actualAttend: actualAttend,
        
        // 迟到早退
        lateTime: lateTime,
        earlyTime: earlyTime,
        
        // 工作时间统计
        workTime: workTime,
        attendanceTime: attendanceTime,
        overtimeTime: overtimeTime,
        
        // 加班分类
        weekdayOvertime: weekdayOvertime,
        weekendOvertime: weekendOvertime,
        holidayOvertime: holidayOvertime,
        
        // 其他字段
        isAbsent: isAbsent === 'True' || isAbsent === '是' || isAbsent === '1' || isAbsent === true,
        isSmartSchedule: isSmartSchedule === 'True' || isSmartSchedule === '是' || isSmartSchedule === '1' || isSmartSchedule === true,
        timeSlot: timeSlot,
        exception: exception,
        shouldCheckIn: shouldCheckIn,
        shouldCheckOut: shouldCheckOut,
        department: department,
        workdayType: workdayType,
        
        // 兼容旧版本字段
        checkIn: formatTime(checkIn, date),
        checkOut: formatTime(checkOut, date),
        status: attendanceStatus,
        rawRecords: [checkIn, checkOut].filter(Boolean),
        
        // 计算字段
        calculatedWorkHours: calculatedWorkHours,
        calculatedOvertimeHours: calculatedOvertimeHours
      });

      processedCount.success++;
    }

    console.log('处理统计:', processedCount);

    // 合并同一员工同一天的多次打卡记录
    const mergedRecords = {};
    attendanceRecords.forEach(record => {
      const key = `${record.employeeCode}-${record.date.toDateString()}`;
      
      if (!mergedRecords[key]) {
        mergedRecords[key] = {
          ...record,
          allCheckIns: record.checkIn ? [record.checkIn] : [],
          allCheckOuts: record.checkOut ? [record.checkOut] : [],
          rawRecords: [...record.rawRecords]
        };
      } else {
        // 合并打卡记录
        if (record.checkIn) {
          mergedRecords[key].allCheckIns.push(record.checkIn);
        }
        if (record.checkOut) {
          mergedRecords[key].allCheckOuts.push(record.checkOut);
        }
        mergedRecords[key].rawRecords.push(...record.rawRecords);
        
        // 更新状态（如果有更严重的状态）
        if (record.status === '缺勤' || 
            (record.status === '迟到' && mergedRecords[key].status === '正常') ||
            (record.status === '早退' && mergedRecords[key].status === '正常')) {
          mergedRecords[key].status = record.status;
        }
      }
    });

    // 为每个合并的记录计算最早签到和最晚签退
    const finalRecords = Object.values(mergedRecords).map(record => {
      // 找出最早的签到时间
      let earliestCheckIn = null;
      if (record.allCheckIns.length > 0) {
        earliestCheckIn = record.allCheckIns.reduce((earliest, current) => {
          return !earliest || current < earliest ? current : earliest;
        });
      }
      
      // 找出最晚的签退时间
      let latestCheckOut = null;
      if (record.allCheckOuts.length > 0) {
        latestCheckOut = record.allCheckOuts.reduce((latest, current) => {
          return !latest || current > latest ? current : latest;
        });
      }
      
      return {
        ...record,
        checkIn: earliestCheckIn,
        checkOut: latestCheckOut,
        // 移除临时字段
        allCheckIns: undefined,
        allCheckOuts: undefined
      };
    });

    console.log(`合并后的记录数: ${finalRecords.length} (原始: ${attendanceRecords.length})`);

    // 查询数据库中现有的员工信息
    const existingEmployees = await Employee.find({}, { name: 1, employeeId: 1 }).limit(10);
    console.log('数据库中现有员工(前10个):', existingEmployees.map(emp => `${emp.name}(${emp.employeeId})`));

    // 查找员工ID并保存
    let savedCount = 0;
    for (const rec of finalRecords) {
      try {
        let emp = null;
        
        // 尝试多种匹配方式
        const employeeCode = rec.employeeCode.toString();
        
        // 1. 直接匹配考勤号码
        emp = await Employee.findOne({ employeeId: employeeCode });
        
        // 2. 考勤号码转换为M+3位数字格式 (1 -> M001)
        if (!emp) {
          const mFormat = `M${employeeCode.padStart(3, '0')}`;
          emp = await Employee.findOne({ employeeId: mFormat });
        }
        
        // 3. 其他常见格式
        if (!emp) {
          const possibleIds = [
            `E${employeeCode.padStart(3, '0')}`, // E001
            `A${employeeCode.padStart(3, '0')}`, // A001
            employeeCode.padStart(4, '0'), // 0001
            `EMP${employeeCode}` // EMP1
          ];
          
          for (const possibleId of possibleIds) {
            emp = await Employee.findOne({ employeeId: possibleId });
            if (emp) break;
          }
        }
        
        // 4. 如果还是没找到，尝试按姓名匹配（需要处理编码问题）
        if (!emp && rec.name) {
          // 尝试解码乱码的姓名
          let decodedName = rec.name;
          try {
            // 尝试从GBK解码
            const buffer = Buffer.from(rec.name, 'latin1');
            decodedName = iconv.decode(buffer, 'gbk');
          } catch (e) {
            // 如果解码失败，保持原名
          }
          
          // 按解码后的姓名查找
          emp = await Employee.findOne({ name: decodedName });
          
          // 如果还是没找到，尝试原姓名
          if (!emp) {
            emp = await Employee.findOne({ name: rec.name });
          }
        }

        console.log(`员工匹配结果 - 考勤号码: ${rec.employeeCode}, 姓名: ${rec.name}, 找到员工: ${emp ? `${emp.name}(${emp.employeeId})` : '未找到'}`);

        if (emp) {
          rec.employeeId = emp._id;
          
          // 保存或更新考勤记录（使用新的数据模型）
          await Attendance.findOneAndUpdate(
            { employeeId: rec.employeeId, date: rec.date },
            {
              employeeId: rec.employeeId,
              employeeCode: rec.employeeCode,
              name: rec.name,
              customId: rec.customId,
              date: rec.date,
              
              // 时间相关字段
              workStartTime: rec.workStartTime,
              workEndTime: rec.workEndTime,
              checkInTime: rec.checkInTime,
              checkOutTime: rec.checkOutTime,
              
              // 应到实到
              shouldAttend: rec.shouldAttend,
              actualAttend: rec.actualAttend,
              
              // 迟到早退
              lateTime: rec.lateTime,
              earlyTime: rec.earlyTime,
              
              // 工作时间统计
              workTime: rec.workTime,
              attendanceTime: rec.attendanceTime,
              overtimeTime: rec.overtimeTime,
              
              // 加班分类
              weekdayOvertime: rec.weekdayOvertime,
              weekendOvertime: rec.weekendOvertime,
              holidayOvertime: rec.holidayOvertime,
              
              // 其他字段
              isAbsent: rec.isAbsent,
              isSmartSchedule: rec.isSmartSchedule,
              timeSlot: rec.timeSlot,
              exception: rec.exception,
              shouldCheckIn: rec.shouldCheckIn,
              shouldCheckOut: rec.shouldCheckOut,
              department: rec.department,
              workdayType: rec.workdayType,
              
              // 兼容旧版本字段
              checkIn: rec.checkIn,
              checkOut: rec.checkOut,
              status: rec.status,
              rawRecords: rec.rawRecords,
              
              // 计算字段
              calculatedWorkHours: rec.calculatedWorkHours,
              calculatedOvertimeHours: rec.calculatedOvertimeHours
            },
            { upsert: true, new: true }
          );
          
          savedCount++;
        } else {
          console.log(`❌ 未找到员工: ${rec.name} (考勤号码: ${rec.employeeCode})`);
        }
      } catch (err) {
        console.error('保存考勤记录失败:', err);
      }
    }

    fs.unlinkSync(filePath);
    
    res.json({ 
      success: true, 
      message: `考勤数据导入完成`,
      stats: {
        total: processedCount.total,
        success: processedCount.success,
        skipped: processedCount.skipped,
        saved: savedCount
      }
    });
  } catch (err) {
    console.error('解析考勤文件失败:', err);
    res.status(500).json({ success: false, message: err.message });
  }
});

// 测试接口：仅分析文件结构，不保存数据
router.post('/analyze', upload.single('attendance'), async (req, res) => {
  try {
    const filePath = req.file.path;
    
    // 尝试不同的编码方式读取文件
    let workbook;
    
    // 检查文件扩展名
    const fileExtension = path.extname(req.file.originalname).toLowerCase();
    
    if (fileExtension === '.csv') {
      // CSV文件特殊处理，支持多种编码
      const buffer = fs.readFileSync(filePath);
      let csvContent = null;
      
      // 尝试不同编码读取CSV
      const encodings = ['utf8', 'gbk', 'gb2312', 'latin1'];
      for (const encoding of encodings) {
        try {
          csvContent = iconv.decode(buffer, encoding);
          // 检查是否包含中文字符且没有乱码
          if (csvContent.includes('考勤') || csvContent.includes('姓名') || csvContent.includes('日期')) {
            console.log(`分析接口成功使用 ${encoding} 编码读取CSV文件`);
            break;
          }
        } catch (e) {
          continue;
        }
      }
      
      if (!csvContent) {
        csvContent = buffer.toString('utf8'); // 默认使用UTF-8
      }
      
      // 解析CSV内容
      workbook = xlsx.read(csvContent, { type: 'string' });
    } else {
      // Excel文件处理
      try {
        workbook = xlsx.readFile(filePath);
      } catch (err) {
        const buffer = fs.readFileSync(filePath);
        const decoded = iconv.decode(buffer, 'gbk');
        workbook = xlsx.read(decoded, { type: 'string' });
      }
    }
    
    const sheetName = workbook.SheetNames[0];
    const sheet = workbook.Sheets[sheetName];
    const jsonData = xlsx.utils.sheet_to_json(sheet, { defval: '' });

    const analysis = {
      totalRows: jsonData.length,
      columns: Object.keys(jsonData[0] || {}),
      sampleData: jsonData.slice(0, 3),
      columnMappings: {
        employeeId: ['¿¼ÇÚºÅÂë', '×Ô¶¨Òå±àºÅ', '考勤号码', '自定义编号', '工号', '员工编号'],
        name: ['ÐÕÃû', '姓名', '员工姓名'],
        date: ['ÈÕÆÚ', '日期', '考勤日期'],
        checkIn: ['Ç©µ½Ê±¼ä', 'ÉÏ°àÊ±¼ä', '签到时间', '上班时间', '签到'],
        checkOut: ['Ç©ÍËÊ±¼ä', 'ÏÂ°àÊ±¼ä', '签退时间', '下班时间', '签退']
      }
    };

    // 查找匹配的列名
    const actualColumns = {};
    const firstRow = jsonData[0] || {};
    
    Object.keys(analysis.columnMappings).forEach(key => {
      for (const possibleName of analysis.columnMappings[key]) {
        if (firstRow.hasOwnProperty(possibleName)) {
          actualColumns[key] = possibleName;
          break;
        }
      }
    });

    analysis.matchedColumns = actualColumns;
    analysis.unmatchedColumns = Object.keys(firstRow).filter(col => !Object.values(actualColumns).includes(col));

    fs.unlinkSync(filePath);
    res.json({ success: true, analysis });
  } catch (err) {
    res.status(500).json({ success: false, message: err.message });
  }
});

// 获取考勤列表API - 支持按月份过滤和统计
router.get('/list', async (req, res) => {
  try {
    const { year, month, employeeId, configId } = req.query;
    
    // 构建查询条件
    const query = {};
    
    if (year && month) {
      const startDate = new Date(year, month - 1, 1);
      const endDate = new Date(year, month, 0, 23, 59, 59);
      query.date = { $gte: startDate, $lte: endDate };
    }
    
    if (employeeId) {
      query.employeeId = employeeId;
    }

    // 获取考勤记录
    const attendanceRecords = await Attendance.find(query)
      .populate('employeeId', 'name employeeId')
      .sort({ date: -1 });

    // 如果指定了年月，进行统计
    if (year && month) {
      // 创建考勤计算器实例
      const calculator = await AttendanceCalculator.create(configId);
      
      // 获取该月的所有员工
      const Employee = require('../models/Employee');
      const allEmployees = await Employee.find({}, 'name employeeId');
      
      // 使用配置化的工作日计算（支持节假日和调休）
      const workDays = await calculator.calculateMonthWorkDays(parseInt(year), parseInt(month));
      
      // 按员工分组统计
      const employeeStats = {};
      
      // 初始化所有员工的统计数据
      allEmployees.forEach(emp => {
        employeeStats[emp._id.toString()] = {
          employeeId: emp.employeeId,
          name: emp.name,
          shouldAttendDays: workDays,
          actualAttendDays: 0,
          absentDays: 0,
          lateDays: 0,
          earlyDays: 0,
          records: [],
          totalWorkHours: 0,
          totalOvertimeHours: 0
        };
      });
      
      // 统计实际考勤数据，使用新的字段和配置化计算
      for (const record of attendanceRecords) {
        if (record.employeeId) {
          const empId = record.employeeId._id.toString();
          if (employeeStats[empId]) {
            // 优先使用新字段，如果没有则使用兼容字段
            const checkInTime = record.checkInTime || record.checkIn;
            const checkOutTime = record.checkOutTime || record.checkOut;
            
            const checkInTimeStr = checkInTime ? 
              `${checkInTime.getHours().toString().padStart(2, '0')}:${checkInTime.getMinutes().toString().padStart(2, '0')}` : 
              null;
            const checkOutTimeStr = checkOutTime ? 
              `${checkOutTime.getHours().toString().padStart(2, '0')}:${checkOutTime.getMinutes().toString().padStart(2, '0')}` : 
              null;
            
            // 使用考勤计算器重新计算状态
            const calculatedStatus = await calculator.calculateAttendanceStatus(
              checkInTimeStr, 
              checkOutTimeStr, 
              record.date
            );
            
            // 更新记录的计算状态
            const enrichedRecord = {
              ...record.toObject(),
              calculatedStatus
            };
            
            employeeStats[empId].records.push(enrichedRecord);
            
            // 判断是否实际出勤（基于上传文件的数据）
            let isActualAttendance = false;
            
            // 1. 优先使用上传文件中的"实到"字段
            if (record.actualAttend === '1' || record.actualAttend === 'True' || record.actualAttend === '是') {
              isActualAttendance = true;
            } 
            // 2. 如果没有"实到"字段，根据签到签退时间判断
            else if (checkInTime || checkOutTime) {
              isActualAttendance = true;
            }
            // 3. 如果明确标记为旷工，则不算出勤
            if (record.isAbsent === true || record.status === '缺勤') {
              isActualAttendance = false;
            }
            
            if (isActualAttendance) {
              employeeStats[empId].actualAttendDays++;
              
              // 使用上传文件中的工作时间数据
              let workHours = 0;
              let overtimeHours = 0;
              
              // 优先使用已计算的字段
              if (record.calculatedWorkHours) {
                workHours = record.calculatedWorkHours;
              } else if (record.workTime) {
                // 从工作时间字符串中提取小时数
                const workTimeMatch = record.workTime.toString().match(/(\d+\.?\d*)/);
                if (workTimeMatch) {
                  workHours = parseFloat(workTimeMatch[1]);
                }
              } else if (calculatedStatus.workHours) {
                workHours = parseFloat(calculatedStatus.workHours);
              }
              
              if (record.calculatedOvertimeHours) {
                overtimeHours = record.calculatedOvertimeHours;
              } else if (record.overtimeTime) {
                // 从加班时间字符串中提取小时数
                const overtimeMatch = record.overtimeTime.toString().match(/(\d+\.?\d*)/);
                if (overtimeMatch) {
                  overtimeHours = parseFloat(overtimeMatch[1]);
                }
              } else if (calculatedStatus.overtimeHours) {
                overtimeHours = parseFloat(calculatedStatus.overtimeHours);
              }
              
              employeeStats[empId].totalWorkHours += workHours;
              employeeStats[empId].totalOvertimeHours += overtimeHours;
              
              // 统计迟到早退（优先使用上传文件中的数据）
              if (record.lateTime && record.lateTime !== '0' && record.lateTime !== '' && record.lateTime !== '00:00') {
                employeeStats[empId].lateDays++;
              } else if (calculatedStatus.isLate) {
                employeeStats[empId].lateDays++;
              }
              
              if (record.earlyTime && record.earlyTime !== '0' && record.earlyTime !== '' && record.earlyTime !== '00:00') {
                employeeStats[empId].earlyDays++;
              } else if (calculatedStatus.isEarly) {
                employeeStats[empId].earlyDays++;
              }
            }
          }
        }
      }
      
      // 计算最终缺勤天数
      Object.values(employeeStats).forEach(stat => {
        stat.absentDays = Math.max(0, stat.shouldAttendDays - stat.actualAttendDays);
        // 格式化工作时间
        stat.totalWorkHours = parseFloat(stat.totalWorkHours.toFixed(2));
        stat.totalOvertimeHours = parseFloat(stat.totalOvertimeHours.toFixed(2));
      });
      
      res.json({
        success: true,
        data: {
          year: parseInt(year),
          month: parseInt(month),
          workDays: workDays,
          employeeStats: Object.values(employeeStats),
          totalRecords: attendanceRecords.length,
          config: {
            name: calculator.config.name,
            workDaysLabels: calculator.getWorkDaysLabels(),
            standardWorkTime: calculator.config.standardWorkTime
          }
        }
      });
    } else {
      // 返回原始记录列表
      res.json({
        success: true,
        data: attendanceRecords
      });
    }
  } catch (error) {
    console.error('获取考勤列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取考勤列表失败',
      error: error.message
    });
  }
});

// 计算工作日数量的辅助函数
function calculateWorkDays(year, month) {
  const date = new Date(year, month - 1, 1);
  const lastDay = new Date(year, month, 0).getDate();
  let workDays = 0;
  
  for (let day = 1; day <= lastDay; day++) {
    date.setDate(day);
    const dayOfWeek = date.getDay();
    // 排除周六(6)和周日(0)
    if (dayOfWeek !== 0 && dayOfWeek !== 6) {
      workDays++;
    }
  }
  
  return workDays;
}


// 根据员工工号和姓名获取考勤数据 - 用于薪资管理
router.get('/employee-attendance', async (req, res) => {
  console.log('请求到达 employee-attendance', req.query);

  try {
    const { employeeId, name, year, month } = req.query;

    if (!employeeId && !name) {
      return res.status(400).json({
        success: false,
        message: '请提供员工工号或姓名'
      });
    }

    const currentDate = new Date();
    const targetYear = year ? parseInt(year) : currentDate.getFullYear();
    const targetMonth = month ? parseInt(month) : currentDate.getMonth() + 1;

    console.log(`获取员工考勤数据: 工号=${employeeId}, 姓名=${name}, ${targetYear}年${targetMonth}月`);

    // 获取考勤配置
    const calculator = await AttendanceCalculator.create();

    // 计算工作日数量
    const workDays = calculateWorkDays(targetYear, targetMonth);

    // 构建查询条件
    const startDate = new Date(targetYear, targetMonth - 1, 1);
    const endDate = new Date(targetYear, targetMonth, 0, 23, 59, 59);

    // 首先查找员工记录 - 优化查询逻辑，处理员工ID映射
    let employeeQuery = {};

    // 处理员工ID映射：M009 -> 9, M001 -> 1 等
    let mappedEmployeeCode = null;
    if (employeeId && employeeId.startsWith('M')) {
      // 提取数字部分，去掉前导零
      const numericPart = employeeId.substring(1);
      mappedEmployeeCode = parseInt(numericPart, 10).toString();
      console.log(`员工ID映射: ${employeeId} -> ${mappedEmployeeCode}`);
    }

    if (employeeId && name) {
      // 同时匹配工号和姓名，使用更宽松的匹配策略
      employeeQuery = {
        $or: [
          { employeeCode: employeeId, name: name },
          { employeeCode: employeeId },
          { name: name },
          // 添加映射后的员工代码
          ...(mappedEmployeeCode ? [{ employeeCode: mappedEmployeeCode, name: name }, { employeeCode: mappedEmployeeCode }] : []),
          // 添加对employeeId字段的匹配（如果存在）
          { 'employeeId.employeeId': employeeId },
          { 'employeeId.name': name }
        ]
      };
    } else if (employeeId) {
      employeeQuery = {
        $or: [
          { employeeCode: employeeId },
          { 'employeeId.employeeId': employeeId },
          // 添加映射后的员工代码
          ...(mappedEmployeeCode ? [{ employeeCode: mappedEmployeeCode }] : [])
        ]
      };
    } else if (name) {
      employeeQuery = {
        $or: [
          { name: name },
          { 'employeeId.name': name }
        ]
      };
    }

    // 添加日期范围查询
    const query = {
      ...employeeQuery,
      date: { $gte: startDate, $lte: endDate }
    };

    console.log('考勤查询条件:', JSON.stringify(query, null, 2));

    const attendanceRecords = await Attendance.find(query)
      .populate('employeeId', 'employeeId name')
      .sort({ date: 1 });

    console.log(`找到 ${attendanceRecords.length} 条考勤记录`);

    if (attendanceRecords.length === 0) {
      // 如果没有找到记录，尝试更宽松的查询
      console.log('未找到考勤记录，尝试更宽松的查询...');

      // 查询所有该月的考勤记录，然后在内存中过滤
      const allRecords = await Attendance.find({
        date: { $gte: startDate, $lte: endDate }
      }).populate('employeeId', 'employeeId name');

      console.log(`该月总共有 ${allRecords.length} 条考勤记录`);

      // 在内存中过滤匹配的记录
      const matchedRecords = allRecords.filter(record => {
        const recordEmployeeCode = record.employeeCode;
        const recordName = record.name;
        const recordEmployeeId = record.employeeId ? record.employeeId.employeeId : null;
        const recordEmployeeName = record.employeeId ? record.employeeId.name : null;

        console.log(`检查记录: employeeCode=${recordEmployeeCode}, name=${recordName}, employeeId=${recordEmployeeId}, employeeName=${recordEmployeeName}`);

        return (employeeId && (recordEmployeeCode === employeeId || recordEmployeeId === employeeId)) ||
               (name && (recordName === name || recordEmployeeName === name));
      });

      console.log(`内存过滤后找到 ${matchedRecords.length} 条匹配记录`);

      if (matchedRecords.length === 0) {
        return res.json({
          success: true,
          data: {
            employeeId: employeeId || '',
            name: name || '',
            shouldAttendDays: workDays,
            actualAttendDays: 0,
            absentDays: workDays,
            lateDays: 0,
            earlyDays: 0,
            totalWorkHours: 0,
            totalOvertimeHours: 0,
            year: targetYear,
            month: targetMonth,
            workDays: workDays,
            records: []
          }
        });
      }

      // 使用匹配的记录
      attendanceRecords.push(...matchedRecords);
    }

    // 统计考勤数据 - 优化统计逻辑
    let actualAttendDays = 0;
    let lateDays = 0;
    let earlyDays = 0;
    let totalWorkHours = 0;
    let totalOvertimeHours = 0;
    const records = [];

    console.log('开始统计考勤数据...');

    for (const record of attendanceRecords) {
      // 判断是否实际出勤 - 优化判断逻辑
      let isActualAttendance = false;

      console.log(`处理记录: 日期=${record.date.toLocaleDateString()}, 实到=${record.actualAttend}, 签到=${record.checkInTime || record.checkIn}, 签退=${record.checkOutTime || record.checkOut}, 状态=${record.status}`);

      // 1. 优先使用上传文件中的"实到"字段
      if (record.actualAttend === '1' || record.actualAttend === 'True' || record.actualAttend === '是' || record.actualAttend === true) {
        isActualAttendance = true;
        console.log('  -> 根据实到字段判断为出勤');
      }
      // 2. 如果没有"实到"字段，根据签到签退时间判断
      else if (record.checkInTime || record.checkOutTime || record.checkIn || record.checkOut) {
        isActualAttendance = true;
        console.log('  -> 根据打卡时间判断为出勤');
      }
      // 3. 根据状态判断
      else if (record.status && record.status !== '缺勤' && record.status !== '旷工' && record.status !== '') {
        isActualAttendance = true;
        console.log('  -> 根据状态判断为出勤');
      }

      // 4. 如果明确标记为旷工或缺勤，则不算出勤
      if (record.isAbsent === true || record.status === '缺勤' || record.status === '旷工') {
        isActualAttendance = false;
        console.log('  -> 明确标记为缺勤/旷工，不算出勤');
      }

      console.log(`  -> 最终判断: ${isActualAttendance ? '出勤' : '未出勤'}`);

      if (isActualAttendance) {
        actualAttendDays++;

        // 统计工作时间
        let workHours = 0;
        let overtimeHours = 0;

        if (record.calculatedWorkHours) {
          workHours = record.calculatedWorkHours;
        } else if (record.workTime) {
          const workTimeMatch = record.workTime.toString().match(/(\d+\.?\d*)/);
          if (workTimeMatch) {
            workHours = parseFloat(workTimeMatch[1]);
          }
        }

        if (record.calculatedOvertimeHours) {
          overtimeHours = record.calculatedOvertimeHours;
        } else if (record.overtimeTime) {
          const overtimeMatch = record.overtimeTime.toString().match(/(\d+\.?\d*)/);
          if (overtimeMatch) {
            overtimeHours = parseFloat(overtimeMatch[1]);
          }
        }

        totalWorkHours += workHours;
        totalOvertimeHours += overtimeHours;

        // 统计迟到早退
        if (record.lateTime && record.lateTime !== '0' && record.lateTime !== '' && record.lateTime !== '00:00') {
          lateDays++;
        }

        if (record.earlyTime && record.earlyTime !== '0' && record.earlyTime !== '' && record.earlyTime !== '00:00') {
          earlyDays++;
        }
      }

      // 添加到记录列表
      records.push({
        date: record.date,
        checkInTime: record.checkInTime || record.checkIn,
        checkOutTime: record.checkOutTime || record.checkOut,
        workTime: record.workTime,
        overtimeTime: record.overtimeTime,
        lateTime: record.lateTime,
        earlyTime: record.earlyTime,
        status: record.status,
        actualAttend: record.actualAttend,
        isActualAttendance: isActualAttendance
      });
    }

    console.log(`统计完成: 实际出勤天数=${actualAttendDays}, 迟到天数=${lateDays}, 早退天数=${earlyDays}`);

    const absentDays = Math.max(0, workDays - actualAttendDays);

    // 获取员工信息
    const employeeInfo = attendanceRecords[0];
    const resultEmployeeId = employeeInfo.employeeId ? employeeInfo.employeeId.employeeId : employeeInfo.employeeCode;
    const resultName = employeeInfo.employeeId ? employeeInfo.employeeId.name : employeeInfo.name;

    res.json({
      success: true,
      data: {
        employeeId: resultEmployeeId,
        name: resultName,
        shouldAttendDays: workDays,
        actualAttendDays: actualAttendDays,
        absentDays: absentDays,
        lateDays: lateDays,
        earlyDays: earlyDays,
        totalWorkHours: parseFloat(totalWorkHours.toFixed(2)),
        totalOvertimeHours: parseFloat(totalOvertimeHours.toFixed(2)),
        year: targetYear,
        month: targetMonth,
        workDays: workDays,
        records: records
      }
    });
  } catch (error) {
    console.error('获取员工考勤数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取员工考勤数据失败',
      error: error.message
    });
  }
});

module.exports = router; 