const express = require('express');
const router = express.Router();
const Salary = require('../models/Salary');
const Employee = require('../models/Employee');
const { calculateMonthlySalary } = require('../utils/SalaryCalculator');
const { analyzeSalaryStructure, generatePayslip } = require('../utils/calculationUtils');
const {
    SALARY_CONFIG,
    LANGUAGE_LEVELS,
    ADMIN_POSITIONS,
    POSITION_TYPES,
    POSITION_LEVELS,
    EDUCATION_LEVELS,
    TITLE_LEVELS,
    PERFORMANCE_LEVELS,
    POSITION_TYPE_MAP
} = require('../config/salaryConfig');

router.use((req, _res, next) => {
    console.log('Salary Route:', req.method, req.path);
    next();
});

// 获取指定月份的工作日信息
router.get('/workdays', async (req, res) => {
    try {
        const { year, month } = req.query;

        if (!year || !month) {
            return res.status(400).json({
                success: false,
                message: '请提供年份和月份参数'
            });
        }

        const WorkDayService = require('../services/WorkDayService');
        const targetYear = parseInt(year);
        const targetMonth = parseInt(month);

        try {
            const workDayResult = await WorkDayService.calculateMonthWorkDays(targetYear, targetMonth);
            res.json({
                success: true,
                data: {
                    year: targetYear,
                    month: targetMonth,
                    requiredAttendance: workDayResult.workDayCount,
                    totalDays: workDayResult.totalDays,
                    weekendDays: workDayResult.weekendDays,
                    holidayDays: workDayResult.holidayDays
                }
            });
        } catch (workDayError) {
            console.error('计算工作日失败:', workDayError);
            // 如果计算工作日失败，使用简单计算
            const startDate = new Date(targetYear, targetMonth - 1, 1);
            const endDate = new Date(targetYear, targetMonth, 0);
            let workDays = 0;

            for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
                const dayOfWeek = d.getDay();
                if (dayOfWeek !== 0 && dayOfWeek !== 6) {
                    workDays++;
                }
            }

            res.json({
                success: true,
                data: {
                    year: targetYear,
                    month: targetMonth,
                    requiredAttendance: workDays,
                    totalDays: endDate.getDate(),
                    weekendDays: null,
                    holidayDays: null
                }
            });
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            message: error.message
        });
    }
});

// 获取所有薪资记录，支持按月份筛选
router.get('/employees', async (req, res) => {
    try {
        const { year, month } = req.query;
        let query = {};

        // 如果提供了年份和月份，则按年月筛选
        if (year && month) {
            query.year = parseInt(year);
            query.month = parseInt(month);
        }

        const salaries = await Salary.find(query).sort({ updatedAt: -1 });

        // 如果有年月参数，计算该月的实际工作日数量
        if (year && month) {
            const WorkDayService = require('../services/WorkDayService');
            const targetYear = parseInt(year);
            const targetMonth = parseInt(month);

            try {
                const workDayResult = await WorkDayService.calculateMonthWorkDays(targetYear, targetMonth);
                const requiredAttendance = workDayResult.workDayCount;

                // 为每个薪资记录添加额定出勤天数，并处理实际出勤天数
                const salariesWithWorkDays = salaries.map(salary => {
                    const salaryObj = salary.toObject();
                    salaryObj.requiredAttendance = requiredAttendance;

                    // 判断是否为高管（通过岗位类型或管理级别）
                    const isExecutive = salaryObj.positionType === '高管' ||
                                       ['总经理', '副总经理', '总监/总工', '副总监/副总工'].includes(salaryObj.administrativeLevel);

                    if (isExecutive) {
                        // 高管的实际出勤天数与额定出勤天数同步
                        salaryObj.actualAttendance = requiredAttendance;
                        console.log(`高管 ${salaryObj.name} 的实际出勤天数设置为: ${requiredAttendance}天`);
                    } else {
                        // 非高管：如果是旧的默认值（22或当前月工作日数），则重置为0
                        if (salaryObj.actualAttendance === 22 ||
                            salaryObj.actualAttendance === requiredAttendance ||
                            !salaryObj.actualAttendance) {
                            salaryObj.actualAttendance = 0;
                        }
                    }
                    // 高管的实际出勤天数保持数据库中的值，不做任何修改

                    return salaryObj;
                });

                console.log(`${targetYear}年${targetMonth}月额定出勤天数: ${requiredAttendance}`);
                res.json(salariesWithWorkDays);
            } catch (workDayError) {
                console.error('计算工作日失败:', workDayError);
                // 如果计算工作日失败，使用默认值22，并处理实际出勤天数
                const salariesWithDefaultWorkDays = salaries.map(salary => {
                    const salaryObj = salary.toObject();
                    salaryObj.requiredAttendance = 22;

                    // 处理实际出勤天数：如果是旧的默认值，则重置为0
                    if (salaryObj.actualAttendance === 22 || !salaryObj.actualAttendance) {
                        salaryObj.actualAttendance = 0;
                    }

                    return salaryObj;
                });
                res.json(salariesWithDefaultWorkDays);
            }
        } else {
            // 如果没有年月参数，使用默认值22，并处理实际出勤天数
            const salariesWithDefaultWorkDays = salaries.map(salary => {
                const salaryObj = salary.toObject();
                salaryObj.requiredAttendance = 22;

                // 判断是否为高管
                const isExecutive = salaryObj.positionType === '高管' ||
                                   ['总经理', '副总经理', '总监/总工', '副总监/副总工'].includes(salaryObj.administrativeLevel);

                if (isExecutive) {
                    // 高管的实际出勤天数与额定出勤天数同步
                    salaryObj.actualAttendance = 22;
                    console.log(`高管 ${salaryObj.name} 的实际出勤天数设置为: 22天（默认）`);
                } else {
                    // 非高管：如果是旧的默认值，则重置为0
                    if (salaryObj.actualAttendance === 22 || !salaryObj.actualAttendance) {
                        salaryObj.actualAttendance = 0;
                    }
                }

                return salaryObj;
            });
            res.json(salariesWithDefaultWorkDays);
        }
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});

// 获取单个员工薪资信息，支持按月份筛选
router.get('/employees/:id', async (req, res) => {
    try {
        const { year, month } = req.query;
        let query = { employeeId: req.params.id };

        // 如果提供了年份和月份，则按年月筛选
        if (year && month) {
            query.year = parseInt(year);
            query.month = parseInt(month);
        }

        const salary = await Salary.findOne(query)
            .populate('employeeId', 'name department subDepartment');
        if (!salary) {
            return res.status(404).json({ message: '未找到该员工的薪资记录' });
        }
        res.json(salary);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});

// 创建或更新薪资记录，支持按月份保存
router.post('/employees', async (req, res) => {
    try {
        const { employeeId, calculationResult, year, month, ...data } = req.body;
        console.log('收到的数据:', JSON.stringify(req.body, null, 2));

        if (!employeeId) {
            return res.status(400).json({ message: '缺少员工ID' });
        }

        // 验证关键字段
        const requiredFields = ['adjustedBaseSalary', 'educationAdjustment', 'languageAdjustment', 'positionSalary'];
        for (const field of requiredFields) {
            if (data[field] === undefined) {
                console.warn(`警告: 缺少字段 ${field}，使用默认值0`);
                data[field] = 0;
            } else if (typeof data[field] !== 'number') {
                console.warn(`警告: 字段 ${field} 不是数字类型，尝试转换`);
                data[field] = Number(data[field]) || 0;
            }
        }

        // 确保系数字段存在
        if (data.educationCoefficient === undefined) {
            console.warn('警告: 缺少字段 educationCoefficient，使用默认值1.0');
            data.educationCoefficient = 1.0;
        } else if (typeof data.educationCoefficient !== 'number') {
            console.warn('警告: 字段 educationCoefficient 不是数字类型，尝试转换');
            data.educationCoefficient = Number(data.educationCoefficient) || 1.0;
        }

        if (data.languageCoefficient === undefined) {
            console.warn('警告: 缺少字段 languageCoefficient，使用默认值1.0');
            data.languageCoefficient = 1.0;
        } else if (typeof data.languageCoefficient !== 'number') {
            console.warn('警告: 字段 languageCoefficient 不是数字类型，尝试转换');
            data.languageCoefficient = Number(data.languageCoefficient) || 1.0;
        }

        // 验证数据一致性
        const { SALARY_CONFIG } = require('../config/salaryConfig');
        const baseSalary = SALARY_CONFIG.BASE_SALARY || 3500; // 从配置中获取基本工资
        const educationAdjustment = Number(data.educationAdjustment) || 0;
        const languageAdjustment = Number(data.languageAdjustment) || 0;
        const isProbation = data.isProbation || false;

        // 检查是否是重置操作 - 更严格的判断
        const isResetOperation = (
            Number(data.adjustedBaseSalary) === 0 &&
            educationAdjustment === 0 &&
            languageAdjustment === 0 &&
            Number(data.educationCoefficient) === 1.0 &&
            Number(data.languageCoefficient) === 1.0 &&
            Number(data.positionSalary || 0) === 0 &&
            Number(data.adminSalary || 0) === 0 &&
            Number(data.performanceBonus || 0) === 0
        );

        console.log('数据一致性检查 - 操作类型判断:', {
            adjustedBaseSalary: data.adjustedBaseSalary,
            adjustedBaseSalaryType: typeof data.adjustedBaseSalary,
            educationAdjustment: educationAdjustment,
            languageAdjustment: languageAdjustment,
            educationCoefficient: data.educationCoefficient,
            educationCoefficientType: typeof data.educationCoefficient,
            languageCoefficient: data.languageCoefficient,
            languageCoefficientType: typeof data.languageCoefficient,
            positionSalary: data.positionSalary,
            adminSalary: data.adminSalary,
            performanceBonus: data.performanceBonus,
            isResetOperation: isResetOperation
        });
        
        // 详细的重置操作判断日志
        console.log('🔍 重置操作判断详细检查:', {
            '步骤1 - Number(data.adjustedBaseSalary) === 0': Number(data.adjustedBaseSalary) === 0,
            '步骤2 - educationAdjustment === 0': educationAdjustment === 0,
            '步骤3 - languageAdjustment === 0': languageAdjustment === 0,
            '步骤4 - Number(data.educationCoefficient) === 1.0': Number(data.educationCoefficient) === 1.0,
            '步骤5 - Number(data.languageCoefficient) === 1.0': Number(data.languageCoefficient) === 1.0,
            '步骤6 - Number(data.positionSalary || 0) === 0': Number(data.positionSalary || 0) === 0,
            '步骤7 - Number(data.adminSalary || 0) === 0': Number(data.adminSalary || 0) === 0,
            '步骤8 - Number(data.performanceBonus || 0) === 0': Number(data.performanceBonus || 0) === 0,
            '最终结果 - isResetOperation': isResetOperation
        });

        // 定义 probationFactor，避免后续使用时未定义
        let probationFactor = isProbation ? 0.8 : 1.0;

        if (isResetOperation) {
            console.log('✅ 检测到重置操作，跳过数据一致性检查，保持重置值不变');
            // 重置操作时，强制使用默认值，不进行任何修复
            data.educationCoefficient = 1.0;
            data.languageCoefficient = 1.0;
            data.educationAdjustment = 0;
            data.languageAdjustment = 0;
            data.adjustedBaseSalary = 0; // 强制保持为0
            data.originalBaseSalary = 0; // 重置时也应该是0，而不是默认的3500
            data.positionSalary = 0;
            data.adminSalary = 0;
            data.performanceBonus = 0;
            data.performanceCoefficient = 1.0;
            
            // 重置操作中的试用期处理
            probationFactor = 1.0; // 重置时不考虑试用期
            
            console.log('重置操作完成，最终数据:', {
                adjustedBaseSalary: data.adjustedBaseSalary,
                educationCoefficient: data.educationCoefficient,
                languageCoefficient: data.languageCoefficient,
                educationAdjustment: data.educationAdjustment,
                languageAdjustment: data.languageAdjustment
            });
        } else {
            // 非重置操作，进行正常的数据一致性检查
            console.log('🔍 进行正常数据一致性检查');
            
            // 计算预期的基本工资（试用期员工也显示原始值，不乘以80%）
            let expectedBaseSalary = baseSalary + educationAdjustment + languageAdjustment;

            // 四舍五入到两位小数
            expectedBaseSalary = Math.round((expectedBaseSalary + Number.EPSILON) * 100) / 100;

            // 获取实际的基本工资
            const actualBaseSalary = Number(data.adjustedBaseSalary);

            // 计算差异（与预期基本工资比较，不考虑试用期系数）
            const difference = Math.abs(expectedBaseSalary - actualBaseSalary);

            // 如果差异大于0.01，则显示警告并修复数据
            if (difference >= 0.01) {
                console.warn('警告：基本工资数据不一致！', {
                    '基本工资': baseSalary,
                    '学历调整值': educationAdjustment,
                    '语言调整值': languageAdjustment,
                    '预期的基本工资': expectedBaseSalary,
                    '实际的基本工资': actualBaseSalary,
                    '差异': difference,
                    '是否试用期': isProbation,
                    '注意': '试用期员工的基本工资不乘以80%，80%折扣只应用于最终应发工资'
                });

                // 修复数据 - 试用期员工也使用原始基本工资
                data.adjustedBaseSalary = expectedBaseSalary;
                data.originalBaseSalary = expectedBaseSalary;
            }
        }

        // 确保试用期相关字段正确
        data.isProbation = isProbation;
        data.probationFactor = probationFactor;

        // 不修改部门信息，删除 department 和 subDepartment 字段
        delete data.department;
        delete data.subDepartment;

        // 设置查询条件，如果提供了年份和月份，则按年月查询
        const query = { employeeId };
        if (year && month) {
            query.year = parseInt(year);
            query.month = parseInt(month);
        }

        console.log('使用查询条件:', query);

        // 确保基础薪资字段在根级别
        const updateData = {
            $set: {
                employeeId,
                ...data,
                // 如果提供了年份和月份，则使用提供的值，否则使用当前年月
                year: year ? parseInt(year) : new Date().getFullYear(),
                month: month ? parseInt(month) : new Date().getMonth() + 1,
                calculationResult: {
                    ...calculationResult,
                    // 保留 absenceDeduction 和 attendanceAdjustment 字段
                    absenceDeduction: calculationResult?.absenceDeduction || 0,
                    attendanceAdjustment: calculationResult?.attendanceAdjustment || 0,
                    // 确保试用期相关字段正确
                    isProbation: data.isProbation || false,
                    probationFactor: data.isProbation ? 0.8 : 1.0,
                    // 移除不需要的字段
                    adjustedBaseSalary: undefined,
                    positionSalary: undefined,
                    adminSalary: undefined,
                    performanceBonus: undefined
                },
                calculatedAt: new Date()
            }
        };

        console.log('更新数据:', JSON.stringify(updateData, null, 2));

        const salary = await Salary.findOneAndUpdate(
            query,
            updateData,
            {
                new: true,
                upsert: true,
                runValidators: true
            }
        );

        console.log('更新后的薪资记录:', JSON.stringify(salary, null, 2));

        res.status(201).json(salary);
    } catch (error) {
        console.error('保存失败:', error);
        res.status(400).json({
            message: error.message,
            errors: error.errors
        });
    }
});

// 更新薪资记录
router.put('/employees/:id', async (req, res) => {
    try {
        const { calculationResult, ...data } = req.body;

        const salary = await Salary.findOneAndUpdate(
            { employeeId: req.params.id },
            {
                $set: {
                    ...data,
                    ...calculationResult,
                    updatedAt: new Date()
                }
            },
            {
                new: true,
                runValidators: true,
                upsert: true
            }
        );

        if (!salary) {
            return res.status(404).json({ message: '未找到该员工的薪资记录' });
        }
        res.json(salary);
    } catch (error) {
        res.status(400).json({ message: error.message });
    }
});

// 删除薪资记录
router.delete('/employees/:id', async (req, res) => {
    try {
        const salary = await Salary.findOneAndDelete({ employeeId: req.params.id });
        if (!salary) {
            return res.status(404).json({ message: '未找到该员工的薪资记录' });
        }
        res.json({ message: '薪资记录已删除' });
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});

// 获取薪资历史记录
router.get('/history/:id', async (req, res) => {
    try {
        const history = await Salary.find({ employeeId: req.params.id })
            .sort({ year: -1, month: -1, calculatedAt: -1 });
        res.json(history);
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
});

// 获取可用的薪资年月列表
router.get('/available-months', async (req, res) => {
    try {
        // 查询所有不同的年月组合
        const result = await Salary.aggregate([
            {
                $group: {
                    _id: { year: "$year", month: "$month" },
                    count: { $sum: 1 }
                }
            },
            {
                $sort: { "_id.year": -1, "_id.month": -1 }
            }
        ]);

        // 格式化结果
        const availableMonths = result.map(item => ({
            year: item._id.year,
            month: item._id.month,
            count: item.count,
            label: `${item._id.year}年${item._id.month}月`
        }));

        res.json(availableMonths);
    } catch (error) {
        console.error('获取可用薪资年月列表失败:', error);
        res.status(500).json({ message: error.message });
    }
});

// 薪资预计算
router.post('/calculate-precalculation', async (req, res) => {
    try {
        console.log('收到薪资预计算请求:', req.body);

        // 使用薪资计算器计算
        const result = calculateMonthlySalary(req.body);

        // 为了与前端组件匹配，添加一个嵌套的 calculationResult 对象
        const formattedResult = {
            ...result,
            calculationResult: {
                mealAllowance: SALARY_CONFIG.MEAL_ALLOWANCE,
                communicationAllowance: SALARY_CONFIG.COMMUNICATION_ALLOWANCE,
                socialInsurance: result.socialInsurance,
                tax: result.tax,
                totalMonthlySalary: result.totalMonthlySalary,
                netSalary: result.netSalary
            }
        };

        console.log('薪资预计算结果:', formattedResult);
        res.json(formattedResult);
    } catch (error) {
        console.error('薪资预计算失败:', error);
        res.status(500).json({ message: error.message });
    }
});

// 获取薪资配置
router.get('/config', (req, res) => {
    try {
        console.log('请求获取薪资配置');
        res.json({
            SALARY_CONFIG,
            LANGUAGE_LEVELS,
            ADMIN_POSITIONS,
            POSITION_TYPES,
            POSITION_LEVELS,
            EDUCATION_LEVELS,
            TITLE_LEVELS,
            PERFORMANCE_LEVELS,
            POSITION_TYPE_MAP
        });
    } catch (error) {
        console.error('获取薪资配置失败:', error);
        res.status(500).json({ message: '获取薪资配置失败', error: error.message });
    }
});

// 计算薪资
router.post('/calculate', async (req, res) => {
    try {
        // 清除 require 缓存，确保使用最新配置
        delete require.cache[require.resolve('../utils/SalaryCalculator')];
        delete require.cache[require.resolve('../utils/calculationUtils')];
        delete require.cache[require.resolve('../config/salaryConfig')];

        // 重新加载计算模块
        const { calculateMonthlySalary } = require('../utils/SalaryCalculator');
        const { analyzeSalaryStructure, generatePayslip } = require('../utils/calculationUtils');

        console.log('收到薪资计算请求，数据:', req.body);

        // 设置默认值
        if (!req.body.administrativeLevel) {
            req.body.administrativeLevel = '无';
        }

        // 增强数据验证 - 只保留路由层特有的验证
        if (!req.body.employeeId) {
            throw new Error('缺少员工ID');
        }

        // 确保数值字段为数字类型
        const numericFields = ['actualAttendance', 'requiredAttendance', 'performanceCoefficient'];
        numericFields.forEach(field => {
            if (req.body[field]) {
                req.body[field] = Number(req.body[field]);
            }
        });

        // 特殊津贴和扣除金额清理
        if (req.body.specialAllowance && req.body.specialAllowance.amount) {
            req.body.specialAllowance.amount = Number(req.body.specialAllowance.amount);
        }
        if (req.body.specialDeduction && req.body.specialDeduction.amount) {
            req.body.specialDeduction.amount = Number(req.body.specialDeduction.amount);
        }

        // 计算薪资
        let monthlyResult;
        try {
            monthlyResult = calculateMonthlySalary(req.body);
            console.log('计算结果:', monthlyResult);
        } catch (calcError) {
            console.error('薪资计算出错:', calcError);
            throw new Error(`薪资计算失败: ${calcError.message}`);
        }

        // 验证计算结果
        if (!monthlyResult ||
            (typeof monthlyResult.calculationResult?.totalMonthlySalary !== 'number' &&
             typeof monthlyResult.totalMonthlySalary !== 'number')) {
            console.error('无效的计算结果:', JSON.stringify(monthlyResult, null, 2));
            throw new Error('薪资计算结果无效');
        }

        // 不再应用额外的考勤系数，因为在 SalaryCalculator.js 中已经考虑了出勤天数的影响

        // 将计算结果中的数字保留小数点后2位，不进行四舍五入
        const adjustedResult = Object.keys(monthlyResult).reduce((acc, key) => {
            if (key === 'calculationResult') {
                acc[key] = Object.keys(monthlyResult[key]).reduce((innerAcc, innerKey) => {
                    if (typeof monthlyResult[key][innerKey] === 'number') {
                        const value = monthlyResult[key][innerKey];
                        // 保留小数点后2位，不进行四舍五入
                        innerAcc[innerKey] = Math.max(0, Math.floor(value * 100) / 100);
                    } else {
                        innerAcc[innerKey] = monthlyResult[key][innerKey];
                    }
                    return innerAcc;
                }, {});
            } else if (typeof monthlyResult[key] === 'number') {
                const value = monthlyResult[key];
                // 保留小数点后2位，不进行四舍五入
                acc[key] = Math.max(0, Math.floor(value * 100) / 100);
            } else {
                acc[key] = monthlyResult[key];
            }
            return acc;
        }, {});

        console.log('调整后结果:', adjustedResult);

        // 确保总薪资非负
        const totalMonthlySalary = adjustedResult.calculationResult?.totalMonthlySalary ||
                                 adjustedResult.totalMonthlySalary || 0;
        if (totalMonthlySalary < 0) {
            console.warn('警告: 总薪资为负数，已调整为0');
            if (adjustedResult.calculationResult) {
                adjustedResult.calculationResult.totalMonthlySalary = 0;
            } else {
                adjustedResult.totalMonthlySalary = 0;
            }
        }

        // 确保所有薪资值非负
        Object.keys(adjustedResult).forEach(key => {
            if (typeof adjustedResult[key] === 'number' && adjustedResult[key] < 0) {
                console.warn(`警告: ${key} 为负数，已调整为0`);
                adjustedResult[key] = 0;
            }
        });
        if (adjustedResult.calculationResult) {
            Object.keys(adjustedResult.calculationResult).forEach(key => {
                if (typeof adjustedResult.calculationResult[key] === 'number' &&
                    adjustedResult.calculationResult[key] < 0) {
                    console.warn(`警告: calculationResult.${key} 为负数，已调整为0`);
                    adjustedResult.calculationResult[key] = 0;
                }
            });
        }

        // 生成薪资分析和工资单
        const salaryStructure = analyzeSalaryStructure(Math.max(0, totalMonthlySalary));
        if (!adjustedResult.totalMonthlySalary && adjustedResult.calculationResult?.totalMonthlySalary) {
            adjustedResult.totalMonthlySalary = adjustedResult.calculationResult.totalMonthlySalary;
        }
        if (adjustedResult.totalMonthlySalary !== undefined) {
            adjustedResult.totalMonthlySalary = Math.max(0, adjustedResult.totalMonthlySalary);
        }
        if (adjustedResult.calculationResult?.totalMonthlySalary !== undefined) {
            adjustedResult.calculationResult.totalMonthlySalary = Math.max(0, adjustedResult.calculationResult.totalMonthlySalary);
        }

        try {
            const payslip = generatePayslip(adjustedResult);
            res.json({
                calculation: adjustedResult,
                structure: salaryStructure,
                payslip: payslip
            });
        } catch (payslipError) {
            console.error('生成工资单失败:', payslipError);
            console.log('调整后的薪资数据:', JSON.stringify(adjustedResult, null, 2));
            throw new Error(`生成工资单失败: ${payslipError.message}`);
        }
    } catch (error) {
        console.error('薪资计算失败:', error);
        res.status(500).json({
            message: '薪资计算失败',
            error: error.message,
            details: process.env.NODE_ENV === 'development' ? error.stack : undefined
        });
    }
});

// 获取薪资统计数据，支持按月份统计
router.get('/stats', async (req, res) => {
    try {
        const { year, month } = req.query;
        const employees = await Employee.find({}, 'employeeId');
        const validEmployeeIds = employees.map(emp => emp.employeeId);

        // 构建查询条件，如果提供了年份和月份，则按年月筛选
        let query = { employeeId: { $in: validEmployeeIds } };
        if (year && month) {
            query.year = parseInt(year);
            query.month = parseInt(month);
        }

        const salaries = await Salary.find(query);

        console.log('后端统计的员工数量:', salaries.length);
        console.log('参与统计的员工:', salaries.map(s => ({
            id: s.employeeId,
            name: s.name,
            year: s.year,
            month: s.month,
            netSalary: s.calculationResult?.netSalary || 0
        })));

        const totalSalary = salaries.reduce((sum, salary) =>
            sum + (salary.calculationResult?.netSalary || 0), 0);

        console.log('后端计算的总薪资:', totalSalary);

        const averageSalary = salaries.length > 0 ? totalSalary / salaries.length : 0;
        const netSalaries = salaries.map(salary => salary.calculationResult?.netSalary || 0);
        const maxSalary = Math.max(...(netSalaries.length ? netSalaries : [0]));
        const minSalary = Math.min(...(netSalaries.filter(s => s > 0).length ?
            netSalaries.filter(s => s > 0) : [0]));

        // 计算总薪资（应发工资总额）
        const totalGrossSalary = salaries.reduce((sum, salary) =>
            sum + (salary.calculationResult?.totalMonthlySalary || 0), 0);

        res.json({
            totalSalary,
            totalNetSalary: totalSalary, // 添加 totalNetSalary 字段，与 totalSalary 相同
            totalGrossSalary,
            averageSalary,
            employeeCount: salaries.length,
            maxSalary,
            minSalary,
            salaryRange: maxSalary - minSalary,
            // 添加年月信息
            year: year ? parseInt(year) : null,
            month: month ? parseInt(month) : null
        });
    } catch (error) {
        console.error('获取薪资统计数据失败:', error);
        res.status(500).json({ message: error.message });
    }
});

// 清理孤立薪资记录
router.delete('/cleanup-orphaned', async (_req, res) => {
    try {
        const employees = await Employee.find({}, 'employeeId');
        const validEmployeeIds = employees.map(emp => emp.employeeId);
        const allSalaries = await Salary.find();
        const orphanedSalaries = allSalaries.filter(salary =>
            !validEmployeeIds.includes(salary.employeeId)
        );

        console.log('发现孤立薪资记录:', orphanedSalaries.map(s => ({
            id: s.employeeId,
            name: s.name,
            netSalary: s.calculationResult?.netSalary || 0
        })));

        const deleteResults = [];
        for (const salary of orphanedSalaries) {
            const result = await Salary.deleteOne({ _id: salary._id });
            deleteResults.push({
                employeeId: salary.employeeId,
                name: salary.name,
                result: result
            });
        }

        res.json({
            message: `已删除 ${orphanedSalaries.length} 条孤立薪资记录`,
            deletedRecords: deleteResults
        });
    } catch (error) {
        console.error('清理孤立薪资记录失败:', error);
        res.status(500).json({ message: error.message });
    }
});

module.exports = router;