const mongoose = require('mongoose');
const HolidayAutoService = require('./services/HolidayAutoService');
const WorkDayService = require('./services/WorkDayService');
const Holiday = require('./models/Holiday');
require('dotenv').config();

async function testAdaptiveSystem() {
    try {
        // 连接数据库
        await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/hrms');
        console.log('已连接到数据库');
        
        console.log('🧪 测试自适应节假日系统\n');
        
        // 1. 清理测试数据
        console.log('1️⃣ 清理测试数据...');
        await Holiday.deleteMany({ year: { $in: [2026, 2027, 2028] } });
        console.log('   ✅ 测试数据清理完成\n');
        
        // 2. 测试自动初始化2026年（未来年份）
        console.log('2️⃣ 测试自动初始化2026年节假日...');
        const result2026 = await HolidayAutoService.autoInitializeYear(2026);
        console.log('   结果:', result2026);
        if (result2026.success) {
            console.log(`   ✅ 成功初始化2026年的${result2026.count}个节假日`);
            console.log(`   节假日: ${result2026.holidays.join(', ')}`);
        }
        console.log('');
        
        // 3. 测试批量初始化
        console.log('3️⃣ 测试批量初始化2027-2028年...');
        const batchResults = await Promise.all([
            HolidayAutoService.autoInitializeYear(2027),
            HolidayAutoService.autoInitializeYear(2028)
        ]);
        batchResults.forEach((result, index) => {
            const year = 2027 + index;
            if (result.success) {
                console.log(`   ✅ ${year}年: ${result.count}个节假日`);
            } else {
                console.log(`   ❌ ${year}年: ${result.error}`);
            }
        });
        console.log('');
        
        // 4. 测试工作日自动配置
        console.log('4️⃣ 测试工作日自动配置...');
        
        // 测试2026年的工作日计算（应该自动触发节假日配置）
        console.log('   测试2026年6月工作日计算:');
        const june2026 = await WorkDayService.calculateMonthWorkDays(2026, 6);
        console.log(`   结果: 应出勤${june2026.workDayCount}天，节假日${june2026.holidayCount}天`);
        
        // 测试2027年春节月份
        console.log('   测试2027年2月工作日计算（春节月）:');
        const feb2027 = await WorkDayService.calculateMonthWorkDays(2027, 2);
        console.log(`   结果: 应出勤${feb2027.workDayCount}天，节假日${feb2027.holidayCount}天，调休${feb2027.weekendWorkDays}天`);
        console.log('');
        
        // 5. 测试系统状态检查
        console.log('5️⃣ 测试系统状态检查...');
        const status = await HolidayAutoService.getHolidayStatus();
        console.log('   当前系统节假日配置状态:');
        Object.keys(status).forEach(year => {
            const yearStatus = status[year];
            console.log(`   ${year}年: ${yearStatus.count}个节假日配置`);
            yearStatus.holidays.forEach(h => {
                console.log(`     - ${h.name}: ${h.holidayDates}天假期, ${h.workDates}天调休`);
            });
        });
        console.log('');
        
        // 6. 测试预测功能
        console.log('6️⃣ 测试节假日预测功能...');
        const predictions = await HolidayAutoService.predictFutureHolidays(2029);
        console.log('   2029年节假日预测:');
        predictions.forEach(pred => {
            console.log(`   - ${pred.name}: 置信度${(pred.confidence * 100).toFixed(0)}% (${pred.suggestion})`);
        });
        console.log('');
        
        // 7. 测试不同年份的工作日对比
        console.log('7️⃣ 测试多年份工作日对比...');
        const years = [2025, 2026, 2027];
        const comparisonData = {};
        
        for (const year of years) {
            const yearData = {};
            for (let month = 1; month <= 12; month++) {
                const stats = await WorkDayService.calculateMonthWorkDays(year, month);
                yearData[month] = stats.workDayCount;
            }
            comparisonData[year] = yearData;
        }
        
        console.log('   各年份每月应出勤天数对比:');
        console.log('   月份  ', years.map(y => `${y}年`).join('  '));
        for (let month = 1; month <= 12; month++) {
            const monthData = years.map(year => comparisonData[year][month].toString().padStart(2, ' '));
            console.log(`   ${month.toString().padStart(2, ' ')}月   ${monthData.join('   ')}`);
        }
        console.log('');
        
        // 8. 验证系统自适应能力
        console.log('8️⃣ 验证系统自适应能力...');
        
        // 删除2026年的部分配置，测试自动重建
        await Holiday.deleteOne({ year: 2026, name: '元旦' });
        console.log('   已删除2026年元旦配置');
        
        // 重新计算，应该自动检测并重建
        const checkResult = await WorkDayService.ensureHolidayConfiguration(2026);
        console.log('   自动检测结果: 系统已尝试重建配置');
        
        // 验证是否重建成功
        const rebuiltHolidays = await Holiday.find({ year: 2026 });
        console.log(`   重建后2026年有${rebuiltHolidays.length}个节假日配置`);
        console.log('');
        
        console.log('🎉 自适应节假日系统测试完成！');
        console.log('\n📋 系统特性总结:');
        console.log('✅ 自动检测缺失的节假日配置');
        console.log('✅ 智能初始化固定和农历节假日');
        console.log('✅ 支持外部API集成（可扩展）');
        console.log('✅ 批量处理多年份配置');
        console.log('✅ 基于历史数据的预测功能');
        console.log('✅ 系统启动时自动检查');
        console.log('✅ 工作日计算自动适配');
        
    } catch (error) {
        console.error('测试失败:', error);
    } finally {
        await mongoose.connection.close();
        console.log('\n数据库连接已关闭');
    }
}

testAdaptiveSystem(); 