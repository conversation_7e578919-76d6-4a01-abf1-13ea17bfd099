// 导入配置
import config from '../config';  // 假设config.js在src/config.js
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { message } from 'antd';
import './Login.css';
import '../styles/ButtonStyles.css';
import { FaEye, FaEyeSlash, FaKey, FaUser } from 'react-icons/fa';


function Login() {
    const [username, setUsername] = useState('');
    const [password, setPassword] = useState('');
    const [showPassword, setShowPassword] = useState(false);
    const navigate = useNavigate();

    // 处理表单提交 NB
    const handleSubmit = (e) => {
        e.preventDefault();
        onSubmit();
    };

    // 修改后的登录函数
    const onSubmit = async () => {
      try {
        // 使用标准化的API路径
        // 使用config中的配置构建API URL
        // 确保路径正确，避免重复的斜杠
        const apiUrl = `${config.apiBaseUrl.replace(/\/$/, '')}/auth/login`;
        console.log('最终请求URL:', apiUrl);

        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username, password })
        });

        // eslint-disable-next-line no-console
        console.log('响应状态:', response.status);

        const responseData = await response.json();
        // eslint-disable-next-line no-console
        console.log('响应数据:', responseData);

        if (!response.ok) {
            throw new Error(responseData.message || `服务器错误 (${response.status})`);
        }

        localStorage.setItem('token', responseData.token);
        // 确保角色信息正确保存
        console.log('原始响应数据:', responseData);
        console.log('原始角色值:', responseData.role);
        console.log('原始角色类型:', typeof responseData.role);

        // 统一使用后端返回的角色信息，确保类型一致
        let userRole = 'user'; // 默认角色
        if (responseData.role) {
            // 将角色转换为字符串并标准化
            const roleStr = String(responseData.role).toLowerCase();
            userRole = roleStr === 'admin' ? 'admin' : 'user';
        }

        console.log('处理后的用户角色:', userRole);

        localStorage.setItem('user', JSON.stringify({
            userId: responseData.userId,
            username: responseData.username,
            phone: responseData.phone,
            role: userRole
        }));

        message.success('登录成功');
        navigate('/dashboard');
    } catch (error) {
        console.error('错误详情:', error);
        if (error instanceof SyntaxError) {
            message.error('服务器返回的数据格式不正确');
        } else {
            message.error('登录失败：' + (error.message || '未知错误'));
        }
    }
};

    return (
        <div className="login-container">
            <div className="login-form">
            <div className="title-container">
                    <div className="page-title-text">欢迎使用</div>
                </div>

                <form onSubmit={handleSubmit}>
                    <div className="form-group">
                        <div className="input-with-icon">
                            <FaUser className="input-icon" />
                            <input
                                type="text"
                                value={username}
                                onChange={(e) => setUsername(e.target.value)}
                                placeholder="请输入用户名"
                                required
                            />
                        </div>
                    </div>
                    <div className="form-group password-group">
                        <div className="password-input-container">
                            <div className="input-with-icon">
                                <FaKey className="input-icon" />
                                <input
                                    type={showPassword ? "text" : "password"}
                                    value={password}
                                    onChange={(e) => setPassword(e.target.value)}
                                    placeholder="请输入密码"
                                    required
                                />
                            </div>
                            <div className="password-actions">
                                <button
                                    type="button"
                                    className="toggle-password"
                                    onClick={() => setShowPassword(!showPassword)}
                                    tabIndex="-1"
                                    style={{ marginLeft: 'auto' }}
                                >
                                    {showPassword ? <FaEyeSlash /> : <FaEye />}
                                </button>
                            </div>
                        </div>
                    </div>
                    <div className="links-container">
                        <span className="text-link" onClick={() => navigate('/register')}>注册新用户</span>
                        <span className="forgot-password-link" onClick={() => navigate('/forgot-password')}>忘记密码？</span>
                        <span className="text-link" onClick={() => navigate('/admin-login')}>管理员入口</span>
                    </div>

                    <div className="login-button-container">
                        <button type="submit" className="btn btn-login">登录</button>
                    </div>
                </form>
                <div style={{ textAlign: 'center', marginTop: '20px', color: '#999', fontSize: '12px' }}>
                    <div style={{ marginBottom: '2px' }}>君正工程ERP系统</div>
                    <div>版本号: MCHRMS-1.3.0</div>
                </div>
            </div>
        </div>
    );
}

export default Login;