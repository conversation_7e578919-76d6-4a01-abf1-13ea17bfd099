import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { message } from 'antd';
import config from '../config';
import './AdminLogin.css'; // 管理员登录专用样式
import '../styles/ButtonStyles.css';
import { <PERSON>a<PERSON>ye, FaEyeSlash, FaKey, FaUser, FaUserShield } from 'react-icons/fa';

const AdminLogin = () => {
    const navigate = useNavigate();
    const [username, setUsername] = useState('');
    const [password, setPassword] = useState('');
    const [loading, setLoading] = useState(false);
    const [showPassword, setShowPassword] = useState(false);

    const handleSubmit = async (e) => {
        e.preventDefault();

        if (!username || !password) {
            message.error('请输入用户名和密码');
            return;
        }

        setLoading(true);

        try {
            const response = await fetch(`${config.apiBaseUrl}/auth/admin-login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ username, password })
            });

            const responseData = await response.json();

            if (!response.ok) {
                throw new Error(responseData.message || '登录失败');
            }

            // 检查是否为管理员
            if (responseData.role !== 'admin') {
                throw new Error('您不是管理员，无法通过此入口登录');
            }

            // 保存登录信息
            localStorage.setItem('token', responseData.token);
            localStorage.setItem('user', JSON.stringify({
                userId: responseData.userId,
                username: responseData.username,
                phone: responseData.phone,
                role: responseData.role
            }));

            message.success('管理员登录成功');

            // 直接导航到管理员模块
            navigate('/admin');
        } catch (error) {
            console.error('登录错误:', error);
            message.error(error.message || '登录失败，请检查用户名和密码');
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="login-container admin-login-container">
            <div className="login-form admin-login-form">
                <div className="admin-badge">管理员专用</div>
                <div className="title-container">
                    <div className="page-title-text admin-page-title-text">管理员登录</div>
                    <p style={{ textAlign: 'center', marginTop: '10px', color: '#666' }}>请输入管理员账号和密码</p>
                </div>

                <form onSubmit={handleSubmit}>
                    <div className="form-group">
                        <div className="input-with-icon">
                            <FaUserShield className="input-icon admin-input-icon" />
                            <input
                                type="text"
                                id="username"
                                value={username}
                                onChange={(e) => setUsername(e.target.value)}
                                placeholder="请输入管理员用户名"
                                required
                            />
                        </div>
                    </div>

                    <div className="form-group password-group">
                        <div className="password-input-container">
                            <div className="input-with-icon">
                                <FaKey className="input-icon admin-input-icon" />
                                <input
                                    type={showPassword ? "text" : "password"}
                                    id="password"
                                    value={password}
                                    onChange={(e) => setPassword(e.target.value)}
                                    placeholder="请输入密码"
                                    required
                                />
                            </div>
                            <div className="password-actions">
                                <button
                                    type="button"
                                    className="toggle-password admin-toggle-password"
                                    onClick={() => setShowPassword(!showPassword)}
                                    tabIndex="-1"
                                    style={{ marginLeft: 'auto' }}
                                >
                                    {showPassword ? <FaEyeSlash /> : <FaEye />}
                                </button>
                            </div>
                        </div>
                    </div>

                    <div className="links-container">
                        <span className="text-link admin-text-link" onClick={() => navigate('/login')}>返回普通登录</span>
                    </div>

                    <div className="login-button-container">
                        <button
                            type="submit"
                            className="btn btn-login admin-btn-login"
                            disabled={loading}
                        >
                            {loading ? '登录中...' : '管理员登录'}
                        </button>
                    </div>
                </form>

                <div style={{ textAlign: 'center', marginTop: '20px', color: '#999', fontSize: '12px' }}>
                    <div style={{ marginBottom: '2px' }}>君正工程ERP系统</div>
                    <div>管理员入口 MCHRMS-1.3.0</div>
                </div>
            </div>
        </div>
    );
};

export default AdminLogin;
