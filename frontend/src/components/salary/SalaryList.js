import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Table, Card, Row, Col, Statistic, message, Input, Select } from 'antd';
import { FileSearchOutlined, EditOutlined, CalculatorOutlined } from '@ant-design/icons';
import './SalaryList.css';
import '../common/TextLinkStyles.css';
import ExportDialog from '../../common/ExportDialog';
import ExportUtils from '../../common/ExportUtils';
import SalaryForm from './SalaryForm';
import SalaryDetail from './SalaryDetail';
import SalaryPrecalculatorModal from './SalaryPrecalculatorModal';
import { useNavigate } from 'react-router-dom';
import { dashboardEvents } from '../Dashboard';
import config from '../../config';
import SalaryService from './SalaryService';
import PrintUtils from '../../common/PrintUtils';
import moment from 'moment';
import { checkNameMatch } from '../../utils/pinyinSearch';
import { 
    formatCurrency, 
    safeNumber, 
    calculateTotalAllowance, 
    processSalaryData,
    createEmptySalaryResult,
    getDefaultSalaryData,
    getPositionType
} from '../../utils/salaryUtils';

const SalaryList = () => {
    const navigate = useNavigate();
    const [salaryData, setSalaryData] = useState([]);
    const [loading, setLoading] = useState(false);
    const [isExportDialogOpen, setIsExportDialogOpen] = useState(false);
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [isPrecalculatorVisible, setIsPrecalculatorVisible] = useState(false);
    const [currentEmployee, setCurrentEmployee] = useState(null);
    const [filterDepartment, setFilterDepartment] = useState('all');
    const [searchText, setSearchText] = useState('');
    const [exportHistory, setExportHistory] = useState({});
    const [localConfig, setLocalConfig] = useState(null);
    const [currentYear, setCurrentYear] = useState(moment().year());
    const [currentMonth, setCurrentMonth] = useState(moment().month() + 1);
    const [currentPage, setCurrentPage] = useState(1);

    const columns = [
        {
            title: '工号 ↑',
            dataIndex: 'employeeId',
            key: 'employeeId',
            align: 'center',
            width: 60,
            sorter: false,
            sortDirections: ['ascend'],
            defaultSortOrder: 'ascend'
        },
        {
            title: '姓名',
            dataIndex: 'name',
            key: 'name',
            align: 'center',
            width: 120
        },
        {
            title: '部门',
            key: 'department',
            align: 'center',
            width: 150,
            render: (_, record) => (
                record.department ? `${record.department}${record.subDepartment ? ` - ${record.subDepartment}` : ''}` : '-'
            )
        },
        {
            title: '工作状态',
            key: 'workStatus',
            align: 'center',
            width: 80,
            render: (_, record) => (
                <span style={{
                    color: record.isProbation ? '#ff4d4f' : 'inherit',
                    fontWeight: record.isProbation ? 'bold' : 'normal',
                    backgroundColor: record.isProbation ? '#fff2f0' : 'transparent',
                    padding: record.isProbation ? '2px 4px' : '0',
                    borderRadius: record.isProbation ? '2px' : '0',
                    border: record.isProbation ? '1px solid #ffccc7' : 'none'
                }}>
                    {record.isProbation ? '试用期' : record.workType || '全职'}
                </span>
            )
        },
        {
            title: '出勤天数',
            align: 'center',
            className: 'attendance-column',
            children: [
                {
                    title: '额定',
                    dataIndex: 'requiredAttendance',
                    key: 'requiredAttendance',
                    align: 'center',
                    className: 'attendance-subcolumn',
                    render: (value) => value || '22'
                },
                {
                    title: '实际',
                    dataIndex: 'actualAttendance',
                    key: 'actualAttendance',
                    align: 'center',
                    className: 'attendance-subcolumn',
                    render: (value) => value || '0'
                }
            ]
        },
        {
            title: '基本工资',
            dataIndex: 'adjustedBaseSalary',
            key: 'adjustedBaseSalary',
            align: 'center',
            width: 80,
            render: (value) => formatCurrency(safeNumber(value))
        },
        {
            title: '岗位工资',
            dataIndex: 'positionSalary',
            key: 'positionSalary',
            align: 'center',
            width: 80,
            render: (value) => formatCurrency(value)
        },
        {
            title: '补贴金额',
            key: 'totalAllowance',
            align: 'center',
            width: 80,
            render: (_, record) => formatCurrency(calculateTotalAllowance(record))
        },
        {
            title: '绩效奖金',
            dataIndex: 'performanceBonus',
            key: 'performanceBonus',
            align: 'center',
            width: 80,
            render: (value) => formatCurrency(value)
        },
        {
            title: '应发工资',
            dataIndex: ['calculationResult', 'totalMonthlySalary'],
            key: 'totalMonthlySalary',
            align: 'center',
            width: 80,
            render: (value) => formatCurrency(value)
        },
        {
            title: '出勤调整',
            dataIndex: ['calculationResult'],
            key: 'attendanceAdjustment',
            align: 'center',
            width: 80,
            render: (calculationResult) => {
                const attendanceAdjustment = safeNumber(calculationResult?.attendanceAdjustment);
                const absenceDeduction = safeNumber(calculationResult?.absenceDeduction);

                if (absenceDeduction > 0) {
                    return formatCurrency(-absenceDeduction);
                } else if (attendanceAdjustment > 0) {
                    return formatCurrency(attendanceAdjustment);
                } else {
                    return formatCurrency(0);
                }
            }
        },
        {
            title: '社保扣除',
            dataIndex: ['calculationResult', 'socialInsurance'],
            key: 'socialInsurance',
            align: 'center',
            width: 80,
            render: (value) => formatCurrency(value)
        },
        {
            title: '专项附加扣除',
            dataIndex: 'specialDeduction',
            key: 'specialDeduction',
            align: 'center',
            width: 80,
            render: (value) => formatCurrency(safeNumber(value?.amount))
        },
        {
            title: '个税扣除',
            dataIndex: ['calculationResult', 'tax'],
            key: 'tax',
            align: 'center',
            width: 80,
            render: (value) => formatCurrency(value)
        },
        {
            title: '实发工资',
            dataIndex: ['calculationResult', 'netSalary'],
            key: 'netSalary',
            align: 'center',
            width: 100,
            render: (value) => formatCurrency(value)
        },
        {
            title: '操作',
            key: 'action',
            align: 'center',
            width: 100,
            render: (_, record) => (
                <div className="action-buttons-container">
                    <span
                        className="text-link-common success"
                        onClick={() => handleViewDetail(record)}
                    >
                        <FileSearchOutlined style={{ marginRight: '4px' }} />
                        详情
                    </span>
                    <span
                        className="text-link-common"
                        onClick={() => handleEdit(record)}
                    >
                        <EditOutlined style={{ marginRight: '4px' }} />
                        编辑
                    </span>
                </div>
            )
        }
    ];

    const statistics = useMemo(() => {
        const totalNetSalary = salaryData.reduce((sum, emp) => sum + safeNumber(emp.calculationResult?.netSalary), 0);
        const totalGrossSalary = salaryData.reduce((sum, emp) => sum + safeNumber(emp.calculationResult?.totalMonthlySalary), 0);
        const netSalaries = salaryData.map(emp => safeNumber(emp.calculationResult?.netSalary));
        
        // 修复最高薪资和最低薪资的计算逻辑
        let maxSalary = 0;
        let minSalary = 0;

        if (netSalaries.length > 0) {
            maxSalary = Math.max(...netSalaries);
            
            // 对于最低薪资，只考虑大于0的薪资
            const positiveSalaries = netSalaries.filter(s => s > 0);
            if (positiveSalaries.length > 0) {
                minSalary = Math.min(...positiveSalaries);
            } else {
                // 如果所有薪资都是0或负数，则最低薪资也是0
                minSalary = 0;
            }
        }

        return {
            totalNetSalary,
            totalGrossSalary,
            averageSalary: salaryData.length ? totalNetSalary / salaryData.length : 0,
            employeeCount: salaryData.length,
            maxSalary,
            minSalary,
            salaryRange: maxSalary - minSalary
        };
    }, [salaryData]);

    const handleEdit = useCallback((record) => {
        console.log('编辑员工:', record.employeeId, record.name);

        const adminLevels = ['总经理', '副总经理', '总监/总工', '副总监/副总工'];
        
        // 使用统一的工具函数处理薪资数据
        const processedRecord = processSalaryData(record, localConfig);

        const employeeInfo = {
            employeeId: record.employeeId,
            name: record.name,
            department: record.department,
            subDepartment: record.subDepartment || '',
            education: processedRecord.education,
            languageLevel: processedRecord.languageLevel,
            firstEducation: record.firstEducation,
            firstSchool: record.firstSchool,
            firstEducationType: record.firstEducationType,
            firstSchoolType: record.firstSchoolType,
            finalEducation: record.finalEducation,
            finalSchool: record.finalSchool,
            finalEducationType: record.finalEducationType,
            finalSchoolType: record.finalSchoolType,
            languageListening: record.languageListening,
            languageSpeaking: record.languageSpeaking,
            languageReading: record.languageReading,
            languageWriting: record.languageWriting,
            workType: record.workType || '全职',
            probationEndDate: record.probationEndDate || '',
            isProbation: record.isProbation || false,
            positionType: adminLevels.includes(record.administrativeLevel) ? '高管' : 
                (localConfig?.SALARY_CONFIG?.POSITION_TYPE_MAP?.[record.position] || '其他'),
            positionLevel: record.positionLevel || '',
            administrativeLevel: record.administrativeLevel || '无',
            specialAllowance: record.specialAllowance || { remark: '', amount: 0 },
            specialDeduction: record.specialDeduction || { amount: 0 },
            year: record.year || currentYear,
            month: record.month || currentMonth
        };

        const salaryInfo = {
            adjustedBaseSalary: safeNumber(record.adjustedBaseSalary),
            originalBaseSalary: safeNumber(record.originalBaseSalary),
            educationAdjustment: safeNumber(record.educationAdjustment),
            languageAdjustment: safeNumber(record.languageAdjustment),
            educationCoefficient: safeNumber(record.educationCoefficient, 1.0),
            languageCoefficient: safeNumber(record.languageCoefficient, 1.0),
            positionSalary: safeNumber(record.positionSalary),
            originalPositionSalary: safeNumber(record.originalPositionSalary),
            adminSalary: safeNumber(record.adminSalary),
            originalAdminSalary: safeNumber(record.originalAdminSalary),
            performanceBonus: safeNumber(record.performanceBonus),
            originalPerformanceBonus: safeNumber(record.originalPerformanceBonus),
            probationFactor: record.probationFactor || 1.0,
            performanceCoefficient: record.performanceCoefficient || 1.0,
            requiredAttendance: record.requiredAttendance || 22,
            actualAttendance: record.actualAttendance !== undefined ? record.actualAttendance : 0,
            specialDeduction: record.specialDeduction || { amount: 0 },
            calculationResult: record.calculationResult || {
                mealAllowance: 0,
                communicationAllowance: 0,
                totalMonthlySalary: 0,
                socialInsurance: 0,
                tax: 0,
                netSalary: 0,
                absenceDeduction: 0,
                attendanceAdjustment: 0,
                isProbation: false,
                probationFactor: 1.0
            }
        };

        setCurrentEmployee(() => {
            const newEmployee = {
                ...employeeInfo,
                ...salaryInfo,
                readOnlyFields: Object.keys(employeeInfo)
            };

            setTimeout(() => {
                setIsModalVisible(true);
            }, 0);

            return newEmployee;
        });
    }, [currentYear, currentMonth, localConfig]);

    // 移除 debounce 函数，直接使用 onChange 和 onSearch 事件

    const filteredData = useMemo(() => {
        // 先过滤数据
        const filteredResult = salaryData.filter(item => {
            // 搜索过滤
            if (searchText && !checkNameMatch(item.name, searchText) && !item.employeeId?.toLowerCase().includes(searchText.toLowerCase())) {
                return false;
            }

            // 如果选择了"全部部门"，则显示所有数据
            if (filterDepartment === 'all') {
                return true;
            }

            // 否则检查部门是否匹配（包含主部门和子部门）
            const fullDepartment = item.department ?
                `${item.department}${item.subDepartment ? ` - ${item.subDepartment}` : ''}`
                : '-';
            return fullDepartment === filterDepartment;
        });

        // 然后按工号排序（升序排列）
        return filteredResult.sort((a, b) => (a.employeeId || '').localeCompare(b.employeeId || ''));
    }, [salaryData, filterDepartment, searchText]);

    const handleDepartmentFilter = useCallback((value) => {
        setFilterDepartment(value);
    }, []);

    const handleTableChange = useCallback((pagination) => {
        setCurrentPage(pagination.current);
    }, []);

    // 先声明 fetchSalaryDataByMonth 函数，但不定义它
    const fetchSalaryDataByMonth = useCallback(async (year, month) => {
        setLoading(true);
        const controller = new AbortController();

        if (!config.apiBaseUrl || !config.apiBaseUrl.startsWith('http')) {
            config.refresh();
        }

        try {
            console.log(`获取 ${year}年${month}月 薪资数据...`);

            // 获取员工数据
            let employees = [];
            let salaries = [];

            try {
                const timestamp = new Date().getTime();
                const employeesResponse = await fetch(`${config.apiBaseUrl}/employees?t=${timestamp}`, {
                    signal: controller.signal,
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token') || ''}`,
                        'Content-Type': 'application/json',
                        'Cache-Control': 'no-cache, no-store, must-revalidate',
                        'Pragma': 'no-cache',
                        'Expires': '0'
                    }
                });

                if (!employeesResponse.ok) {
                    throw new Error(`获取员工数据失败: ${employeesResponse.status}`);
                }

                employees = await employeesResponse.json();
                console.log('成功获取员工数据，数量:', employees.length);
            } catch (empError) {
                console.error('获取员工数据出错:', empError);
                message.error('获取员工数据失败，请检查网络连接');
                throw empError;
            }

            try {
                const timestamp = new Date().getTime();
                const salariesResponse = await fetch(`${config.apiBaseUrl}/salary/employees?year=${year}&month=${month}&t=${timestamp}`, {
                    signal: controller.signal,
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token') || ''}`,
                        'Content-Type': 'application/json',
                        'Cache-Control': 'no-cache, no-store, must-revalidate',
                        'Pragma': 'no-cache',
                        'Expires': '0'
                    }
                });

                if (salariesResponse.ok) {
                    salaries = await salariesResponse.json();
                    console.log(`成功获取 ${year}年${month}月 薪资数据，数量:`, salaries.length);
                } else {
                    console.warn('获取薪资数据失败，使用空数组:', salariesResponse.status);
                    salaries = [];
                }
            } catch (salaryError) {
                console.warn('获取薪资数据出错，使用空数组:', salaryError);
                salaries = [];
            }

            // 获取当月工作日数量（用于没有薪资数据的情况）
            let defaultRequiredAttendance = 22; // 默认值
            try {
                const workdaysResponse = await fetch(`${config.apiBaseUrl}/salary/workdays?year=${year}&month=${month}`, {
                    signal: controller.signal,
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token') || ''}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (workdaysResponse.ok) {
                    const workdaysData = await workdaysResponse.json();
                    if (workdaysData.success) {
                        defaultRequiredAttendance = workdaysData.data.requiredAttendance;
                        console.log(`从后端获取的${year}年${month}月工作日数量: ${defaultRequiredAttendance}天`);
                    }
                } else {
                    console.warn('获取工作日数据失败，使用前端计算');
                    // 前端简单计算作为备选
                    const startDate = new Date(year, month - 1, 1);
                    const endDate = new Date(year, month, 0);
                    let workDays = 0;

                    for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
                        const dayOfWeek = d.getDay();
                        if (dayOfWeek !== 0 && dayOfWeek !== 6) { // 排除周末
                            workDays++;
                        }
                    }
                    defaultRequiredAttendance = workDays;
                    console.log(`前端计算的${year}年${month}月工作日数量: ${workDays}天`);
                }
            } catch (error) {
                console.warn('获取工作日数据失败，使用默认值22:', error);
            }

            // 合并员工和薪资数据
            const combinedSalaryData = employees.map(employee => {
                const salaryInfo = salaries.find(s => s.employeeId === employee.employeeId) || {};

                // 确定岗位类型
                const positionType = getPositionType(employee, localConfig);

                console.log('[DEBUG] 正在处理员工数据:', employee);

                // 根据岗位类型设置默认岗位等级
                let defaultPositionLevel = 'D1';
                if (positionType === '技术') {
                    defaultPositionLevel = 'A1';
                } else if (positionType === '高管') {
                    defaultPositionLevel = 'B1';
                } else if (positionType === '支持') {
                    defaultPositionLevel = 'C1';
                } else if (positionType === '其他') {
                    defaultPositionLevel = 'D1';
                }

                // 基础员工数据
                const baseData = {
                    ...employee,
                    department: employee.department || '-',
                    subDepartment: employee.subDepartment || '',
                    year,
                    month,
                    workType: employee.workType || '全职',
                    probationEndDate: employee.probationEndDate || '',
                    isProbation: employee.workType === '试用' && employee.probationEndDate && new Date() <= new Date(employee.probationEndDate),
                    positionType: positionType,  // 添加岗位类型
                };

                // 合并薪资数据
                const combinedData = {
                    ...baseData,
                    positionLevel: salaryInfo.positionLevel || defaultPositionLevel,  // 使用默认岗位等级
                    adjustedBaseSalary: safeNumber(salaryInfo.adjustedBaseSalary),
                    originalBaseSalary: safeNumber(salaryInfo.originalBaseSalary),
                    educationAdjustment: safeNumber(salaryInfo.educationAdjustment),
                    languageAdjustment: safeNumber(salaryInfo.languageAdjustment),
                    educationCoefficient: safeNumber(salaryInfo.educationCoefficient, 1.0),
                    languageCoefficient: safeNumber(salaryInfo.languageCoefficient, 1.0),
                    positionSalary: safeNumber(salaryInfo.positionSalary),
                    originalPositionSalary: safeNumber(salaryInfo.originalPositionSalary),
                    adminSalary: safeNumber(salaryInfo.adminSalary),
                    originalAdminSalary: safeNumber(salaryInfo.originalAdminSalary),
                    performanceBonus: safeNumber(salaryInfo.performanceBonus),
                    originalPerformanceBonus: safeNumber(salaryInfo.originalPerformanceBonus),
                    probationFactor: salaryInfo.probationFactor || 1.0,
                    performanceCoefficient: salaryInfo.performanceCoefficient || 1.0,
                    actualAttendance: (() => {
                        // 高管的实际出勤天数应该与额定出勤天数同步
                        if (positionType === '高管') {
                            // 如果有薪资数据，使用薪资数据中的值
                            if (salaryInfo.actualAttendance !== undefined && salaryInfo.actualAttendance > 0) {
                                return salaryInfo.actualAttendance;
                            }
                            // 否则使用额定出勤天数
                            return salaryInfo.requiredAttendance || defaultRequiredAttendance;
                        }

                        // 非高管：如果有明确的考勤数据且不是默认值，则使用该值
                        if (salaryInfo.actualAttendance &&
                            salaryInfo.actualAttendance !== 22 &&
                            salaryInfo.actualAttendance !== defaultRequiredAttendance) {
                            return salaryInfo.actualAttendance;
                        }
                        // 否则默认为0，表示需要手动获取考勤数据
                        return 0;
                    })(),
                    requiredAttendance: salaryInfo.requiredAttendance || defaultRequiredAttendance,  // 使用计算的工作日数量
                    specialAllowance: salaryInfo.specialAllowance || { remark: '', amount: 0 },
                    specialDeduction: salaryInfo.specialDeduction || { amount: 0 },
                    calculationResult: salaryInfo.calculationResult || {
                        mealAllowance: 0,
                        communicationAllowance: 0,
                        totalMonthlySalary: 0,
                        socialInsurance: 0,
                        tax: 0,
                        netSalary: 0,
                        absenceDeduction: 0,
                        attendanceAdjustment: 0,
                        isProbation: false,
                        probationFactor: 1.0
                    }
                };

                // 使用统一工具函数处理数据（主要是处理学历和语言等级）
                return localConfig ? processSalaryData(combinedData, localConfig) : combinedData;
            });

            setSalaryData(combinedSalaryData);
        } catch (error) {
            if (error.name === 'AbortError') return;
            console.error('获取薪资数据失败:', error);
            message.error('获取薪资数据失败: ' + error.message);
        } finally {
            setLoading(false);
        }
    }, [localConfig]);

    // 现在定义 handleFormSubmit，它可以安全地引用 fetchSalaryDataByMonth
    const handleFormSubmit = useCallback(async (updatedEmployee) => {
        try {
            console.log('表单提交的数据:', updatedEmployee);

            console.log('准备通知仪表盘更新数据...');
            dashboardEvents.publish('salary-updated', { refresh: true });
            console.log('已通知仪表盘更新数据');

            message.success('保存成功');

            // 重新获取数据
            console.log('重新获取数据...');
            fetchSalaryDataByMonth(currentYear, currentMonth);
        } catch (error) {
            console.error('保存失败:', error);
            message.error('保存失败: ' + error.message);
        }
    }, [currentYear, currentMonth, fetchSalaryDataByMonth]);



    const initSalaryData = useCallback(() => {
        const now = moment();
        setCurrentYear(now.year());
        setCurrentMonth(now.month() + 1);
        fetchSalaryDataByMonth(now.year(), now.month() + 1);
    }, [fetchSalaryDataByMonth]);

    useEffect(() => {
        initSalaryData();

        // 添加事件监听器，响应数据库恢复事件
        const handleDatabaseRestored = () => {
            console.log('收到数据库恢复事件，重新获取薪资数据...');
            initSalaryData();
        };

        // 添加事件监听器，响应刷新薪资数据事件
        const handleRefreshSalaries = () => {
            console.log('收到刷新薪资数据事件，重新获取薪资数据...');
            initSalaryData();
        };

        // 注册事件监听器
        window.addEventListener('database-restored', handleDatabaseRestored);
        window.addEventListener('refresh-salaries', handleRefreshSalaries);

        // 清理函数
        return () => {
            window.removeEventListener('database-restored', handleDatabaseRestored);
            window.removeEventListener('refresh-salaries', handleRefreshSalaries);
        };
    }, [initSalaryData]);

    useEffect(() => {
        let mounted = true;
        async function fetchConfig() {
            try {
                const cfg = await SalaryService.getSalaryConfig();
                if (mounted) {
                    setLocalConfig(cfg);
                    console.log('成功获取薪资配置:', cfg);
                }
            } catch (e) {
                console.error('获取薪资配置失败:', e);
                if (mounted) setLocalConfig({ SALARY_CONFIG: {} });
            }
        }
        fetchConfig();

        // 添加事件监听器，响应刷新配置事件
        const handleRefreshConfig = () => {
            console.log('收到刷新配置事件，重新获取薪资配置...');
            fetchConfig();
        };

        // 注册事件监听器
        window.addEventListener('refresh-config', handleRefreshConfig);

        return () => {
            mounted = false;
            window.removeEventListener('refresh-config', handleRefreshConfig);
        };
    }, []);

    const [isDetailModalVisible, setIsDetailModalVisible] = useState(false);
    const [currentDetailEmployee, setCurrentDetailEmployee] = useState(null);

    // 重置所有员工薪资为0
    const handleResetAllSalaries = useCallback(async () => {
        console.log('🚀 重置功能被调用');
        
        if (!window.confirm(`确定要重置 ${currentYear}年${currentMonth}月 所有员工的薪资吗？此操作不可撤销。`)) {
            console.log('❌ 用户取消了重置操作');
            return;
        }

        console.log('✅ 用户确认重置操作');

        try {
            setLoading(true);
            message.loading('正在重置所有员工薪资...', 0);

            const employees = salaryData.map(emp => ({
                employeeId: emp.employeeId,
                name: emp.name,
                department: emp.department,
                subDepartment: emp.subDepartment,
                isProbation: emp.isProbation || false,
                workType: emp.workType || '全职',
                probationEndDate: emp.probationEndDate || '',
                education: emp.education,
                languageLevel: emp.languageLevel,
                administrativeLevel: emp.administrativeLevel || '无',
                positionType: emp.positionType || '其他',
                year: currentYear,
                month: currentMonth
            }));

            console.log(`🎯 准备重置 ${employees.length} 名员工的薪资...`);

            const resetPromises = employees.map(async (emp, index) => {
                try {
                    console.log(`🔄 正在重置员工 ${index + 1}/${employees.length}: ${emp.name}(${emp.employeeId})`);
                    
                    // 使用默认薪资数据函数
                    const zeroSalaryData = {
                        ...getDefaultSalaryData(emp),
                        year: currentYear,
                        month: currentMonth
                    };

                    const result = await SalaryService.saveSalary(zeroSalaryData);
                    console.log(`✅ 员工 ${emp.name} 重置成功`);
                    return result;
                } catch (error) {
                    console.error(`❌ 重置员工 ${emp.name}(${emp.employeeId}) 薪资失败:`, error);
                    return null;
                }
            });

            console.log('⏳ 等待所有重置操作完成...');
            const results = await Promise.all(resetPromises);
            const successCount = results.filter(result => result !== null).length;

            message.destroy();

            if (successCount === employees.length) {
                message.success(`成功重置 ${successCount} 名员工的薪资为0`);
                console.log('🎉 所有员工薪资重置成功');
            } else {
                message.warning(`部分重置成功: ${successCount}/${employees.length} 名员工的薪资已重置为0`);
                console.log('⚠️ 部分员工薪资重置失败');
            }

            console.log('🔄 重新获取薪资数据...');
            fetchSalaryDataByMonth(currentYear, currentMonth);
        } catch (error) {
            message.destroy();
            console.error('💥 重置所有薪资失败:', error);
            message.error('重置所有薪资失败: ' + error.message);
        } finally {
            setLoading(false);
            console.log('🏁 重置操作完成');
        }
    }, [currentYear, currentMonth, salaryData, fetchSalaryDataByMonth]);

    const handleViewDetail = useCallback((record) => {
        console.log('查看详情 - 员工:', record.employeeId, record.name);

        // 使用统一工具函数处理薪资数据
        const processedRecord = processSalaryData(record, localConfig);

        setCurrentDetailEmployee(processedRecord);
        setIsDetailModalVisible(true);
    }, [localConfig]);

    const handleExportWithFormat = useCallback((fileName, fileFormat) => {
        if (!salaryData || salaryData.length === 0) {
            message.warning('没有可导出的数据');
            return;
        }

        let finalFileName = fileName;
        const fileKey = `${fileName}.${fileFormat}`;

        if (exportHistory[fileKey]) {
            const count = exportHistory[fileKey] + 1;
            finalFileName = `${fileName}(${count})`;
            setExportHistory(prev => ({ ...prev, [fileKey]: count }));
        } else {
            setExportHistory(prev => ({ ...prev, [fileKey]: 1 }));
        }

        const convertToChinese = (num) => {
            if (isNaN(num)) return '';
            const fraction = ['角', '分'];
            const digit = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
            const unit = [['元', '万', '亿'], ['', '拾', '佰', '仟']];
            let head = num < 0 ? '负' : '';
            num = Math.abs(num);
            let s = '';
            for (let i = 0; i < fraction.length; i++) {
                s += (digit[Math.floor(num * 10 * Math.pow(10, i)) % 10] + fraction[i]).replace(/零./, '');
            }
            s = s || '整';
            num = Math.floor(num);
            for (let i = 0; i < unit[0].length && num > 0; i++) {
                let p = '';
                for (let j = 0; j < unit[1].length && num > 0; j++) {
                    p = digit[num % 10] + unit[1][j] + p;
                    num = Math.floor(num / 10);
                }
                s = p.replace(/(零.)*零$/, '').replace(/^$/, '零') + unit[0][i] + s;
            }
            return head + s.replace(/(零.)*零元/, '元').replace(/(零.)+/g, '零').replace(/^整$/, '零元整');
        };

        const exportData = filteredData.map(item => {
            const totalAllowance = calculateTotalAllowance(item);
            const attendanceAdjustment = safeNumber(item.calculationResult?.attendanceAdjustment);
            const absenceDeduction = safeNumber(item.calculationResult?.absenceDeduction);
            
            let attendanceValue = 0;
            if (absenceDeduction > 0) {
                attendanceValue = -absenceDeduction;
            } else if (attendanceAdjustment > 0) {
                attendanceValue = attendanceAdjustment;
            }

            return {
                工号: item.employeeId || '',
                姓名: item.name || '',
                部门: item.department ? `${item.department}${item.subDepartment ? ` - ${item.subDepartment}` : ''}` : '',
                应出勤天数: item.requiredAttendance || 22,
                实际出勤天数: item.actualAttendance || 0,
                基本工资: Math.floor(safeNumber(item.adjustedBaseSalary)),
                岗位工资: safeNumber(item.positionSalary),
                补贴金额: totalAllowance,
                绩效奖金: safeNumber(item.performanceBonus),
                应发工资: safeNumber(item.calculationResult?.totalMonthlySalary),
                出勤调整: attendanceValue,
                社保扣除: safeNumber(item.calculationResult?.socialInsurance),
                个税扣除: safeNumber(item.calculationResult?.tax),
                专项扣除: safeNumber(item.specialDeduction?.amount),
                实发工资: safeNumber(item.calculationResult?.netSalary),
                领款人签字: ''
            };
        });

        const totalMonthlySalary = filteredData.reduce((sum, item) => sum + safeNumber(item.calculationResult?.totalMonthlySalary), 0);
        const totalSocialInsurance = filteredData.reduce((sum, item) => sum + safeNumber(item.calculationResult?.socialInsurance), 0);
        const totalTax = filteredData.reduce((sum, item) => sum + safeNumber(item.calculationResult?.tax), 0);
        const totalNetSalary = filteredData.reduce((sum, item) => sum + safeNumber(item.calculationResult?.netSalary), 0);

        exportData.push({
            工号: '合计',
            姓名: '',
            部门: '',
            应出勤天数: '',
            实际出勤天数: '',
            基本工资: '',
            岗位工资: '',
            补贴金额: '',
            绩效奖金: '',
            应发工资: Number(totalMonthlySalary),
            出勤调整: '',
            社保扣除: Number(totalSocialInsurance),
            个税扣除: Number(totalTax),
            专项扣除: '',
            实发工资: Number(totalNetSalary),
            领款人签字: ''
        });

        const exportOptions = {
            title: `${currentYear}年${currentMonth}月薪资列表`,
            headers: ['工号', '姓名', '部门', '应出勤天数', '实际出勤天数', '基本工资', '岗位工资', '补贴金额', '绩效奖金', '应发工资', '出勤调整', '社保扣除', '个税扣除', '专项扣除', '实发工资', '领款人签字'],
            fields: ['工号', '姓名', '部门', '应出勤天数', '实际出勤天数', '基本工资', '岗位工资', '补贴金额', '绩效奖金', '应发工资', '出勤调整', '社保扣除', '个税扣除', '专项扣除', '实发工资', '领款人签字'],
            moduleType: '薪资列表',
            customRows: [
                {
                    content: `实发金额（人民币）：￥${totalNetSalary.toFixed(2)}（${convertToChinese(totalNetSalary)}）`,
                    colSpan: 16
                },
                {
                    cells: [
                        { content: '制表：', colSpan: 4 },
                        { content: '复核：', colSpan: 4 },
                        { content: '部门负责人：', colSpan: 4 },
                        { content: '总经理：', colSpan: 4 }
                    ]
                }
            ]
        };

        switch (fileFormat) {
            case 'xlsx':
            case 'xls':
                ExportUtils.exportData(exportData, finalFileName, 'xlsx', exportOptions);
                break;
            case 'pdf':
                ExportUtils.exportData(exportData, finalFileName, 'pdf', exportOptions);
                break;
            case 'doc':
            case 'docx':
                ExportUtils.exportData(exportData, finalFileName, 'word', exportOptions);
                break;
            default:
                console.error('不支持的文件格式');
        }
        setIsExportDialogOpen(false);
    }, [salaryData, filteredData, currentYear, currentMonth, exportHistory]);

    return (
        <div className="salary-management" style={{ width: '100%', maxWidth: '1700px', padding: '0 20px', margin: '0 auto' }}>
            <div className="content-wrapper" style={{ width: '100%', margin: '0 auto' }}>
                <h2 style={{ textAlign: 'center', margin: '20px 0' }}>员工薪资表（{currentYear}年{currentMonth}月）</h2>

                <div className="action-filter-bar">
                    <div className="filter-area">
                        <div className="search-group">
                            <label>搜索：</label>
                            <Input
                                placeholder="姓名/拼音首字母/工号"
                                allowClear
                                onChange={(e) => setSearchText(e.target.value)}
                                style={{ width: 168, borderRadius: '6px', height: '32px' }}
                                className="salary-search-input"
                            />
                        </div>
                        <div className="department-group">
                            <label>部门：</label>
                            <Select
                                placeholder="选择部门"
                                onChange={handleDepartmentFilter}
                                style={{ width: 130, borderRadius: '6px' }}
                                defaultValue="all"
                                showSearch
                                optionFilterProp="children"
                                className="salary-department-select"
                                dropdownStyle={{
                                    maxHeight: 400,
                                    overflow: 'auto',
                                    borderRadius: '6px',
                                    boxShadow: '0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05)'
                                }}
                                listHeight={400}
                            >
                                <Select.Option value="all" style={{ fontWeight: 'bold', borderBottom: '1px solid #f0f0f0', padding: '8px 12px', backgroundColor: '#f0f7ff' }}>
                                    全部部门
                                </Select.Option>
                                {Array.from(new Set(salaryData.map(item => {
                                    const fullDepartment = item.department ? `${item.department}${item.subDepartment ? ` - ${item.subDepartment}` : ''}` : '-';
                                    return fullDepartment;
                                }))).map(dept => (
                                    <Select.Option key={dept} value={dept} style={{ padding: '8px 12px', borderBottom: '1px solid #f5f5f5' }}>{dept}</Select.Option>
                                ))}
                            </Select>
                        </div>
                        <div className="month-group">
                            <label>薪资月份：</label>
                            <Select
                                placeholder="选择月份"
                                value={`${currentYear}-${currentMonth}`}
                                onChange={(value) => {
                                    const [year, month] = value.split('-').map(Number);
                                    setCurrentYear(year);
                                    setCurrentMonth(month);
                                    fetchSalaryDataByMonth(year, month);
                                }}
                                style={{ width: 120, borderRadius: '6px' }}
                                className="month-picker"
                            >
                                {Array.from({ length: 36 }).map((_, index) => {
                                    const date = moment().subtract(index, 'months');
                                    const year = date.year();
                                    const month = date.month() + 1;
                                    return (
                                        <Select.Option key={`${year}-${month}`} value={`${year}-${month}`}>
                                            {`${year}年${month}月`}
                                        </Select.Option>
                                    );
                                })}
                            </Select>
                        </div>
                    </div>
                    <div className="action-buttons-group">
                        <button className="action-button back-button" onClick={() => navigate('/dashboard')}>
                            <i className="button-icon">←</i>
                            返回
                        </button>
                        <button className="action-button export-button" onClick={() => setIsExportDialogOpen(true)}>
                            <i className="button-icon">↓</i>
                            导出
                        </button>
                        <button className="action-button print-button" onClick={() => {
                            const printData = filteredData;
                            const printColumns = columns.map(col => ({ ...col, render: col.key === 'action' ? undefined : col.render }));
                            printColumns.push({
                                title: '领款人签字',
                                key: 'signature',
                                dataIndex: 'signature',
                                align: 'center',
                                width: 100,
                                render: () => ''
                            });

                            PrintUtils.printContent(
                                `${currentYear}年${currentMonth}月薪资明细表`,
                                printData,
                                printColumns,
                                {
                                    '应发薪资总额': statistics.totalGrossSalary,
                                    '实发薪资总额': statistics.totalNetSalary,
                                    '平均薪资': statistics.averageSalary,
                                    '员工总数': statistics.employeeCount
                                },
                                {
                                    companyName: 'HR管理系统',
                                    year: currentYear,
                                    month: currentMonth
                                }
                            );
                        }}>
                            <i className="button-icon">🖨</i>
                            打印
                        </button>
                        <button className="action-button budget-button" onClick={() => setIsPrecalculatorVisible(true)}>
                            <CalculatorOutlined className="button-icon" />
                            薪资测算
                        </button>
                        <button className="action-button reset-button" onClick={handleResetAllSalaries}>
                            <i className="button-icon">⟲</i>
                            重置薪资
                        </button>
                    </div>
                </div>

                <div className="statistics-cards">
                    <Row gutter={[8, 8]} style={{ margin: '0 auto', width: '100%' }}>
                        {[
                            { title: "应发薪资总额", value: statistics.totalGrossSalary },
                            { title: "实发薪资总额", value: statistics.totalNetSalary },
                            { title: "平均薪资", value: statistics.averageSalary },
                            { title: "最高薪资", value: statistics.maxSalary },
                            { title: "最低薪资", value: statistics.minSalary },
                            { title: "员工总数", value: statistics.employeeCount, noPrefix: true }
                        ].map(item => (
                            <Col xs={12} sm={8} md={6} lg={4} key={item.title} style={{ display: 'flex' }}>
                                <Card style={{ width: '100%', flex: 1, margin: '0 2px' }}>
                                    <Statistic
                                        title={item.title}
                                        value={item.value}
                                        precision={item.noPrefix ? 0 : 2}
                                        prefix={item.noPrefix ? null : "¥"}
                                    />
                                </Card>
                            </Col>
                        ))}
                    </Row>
                    <div style={{
                        margin: '10px auto',
                        padding: '8px 12px',
                        backgroundColor: '#fff2f0',
                        border: '1px solid #ffccc7',
                        borderRadius: '4px',
                        fontSize: '13px',
                        color: '#434343',
                        width: 'fit-content'
                    }}>
                        <span style={{ fontWeight: 'bold', color: '#ff4d4f' }}>试用期员工薪资说明：</span> 处于试用期的员工，其税前应发工资自动按照正常工资的80%计算，包括基本工资、岗位工资、餐补、通讯补贴和特殊津贴等所有薪资组成部分。
                    </div>
                </div>

                <div className="table-container">
                    <Table
                        columns={columns}
                        dataSource={filteredData}
                        loading={loading || !localConfig}
                        rowKey="employeeId"
                        scroll={{ x: 'max-content' }}
                        bordered
                        size="small"
                        tableLayout="fixed"
                        onChange={handleTableChange}
                        pagination={{
                            position: ['bottomCenter'],
                            showSizeChanger: true,
                            showQuickJumper: true,
                            style: { padding: '16px 0' },
                            total: filteredData.length,
                            pageSize: 10,
                            current: currentPage,
                            showTotal: (total) => (
                                <span>
                                    第 <Input
                                        style={{ width: '45px', margin: '0 8px', textAlign: 'center' }}
                                        onPressEnter={(e) => {
                                            const page = parseInt(e.target.value);
                                            if (page && page > 0 && page <= Math.ceil(total / 10)) {
                                                e.target.blur();
                                                setCurrentPage(page);
                                            }
                                        }}
                                    /> 页/共 {Math.ceil(total / 10)} 页
                                </span>
                            ),
                            itemRender: (_, type, originalElement) => {
                                if (type === 'prev') {
                                    return <button className="page-button">上一页</button>;
                                }
                                if (type === 'next') {
                                    return <button className="page-button">下一页</button>;
                                }
                                return originalElement;
                            }
                        }}
                    />
                </div>

                {isModalVisible && (
                    <SalaryForm
                        visible={isModalVisible}
                        initialValues={currentEmployee || {}}
                        onCancel={() => {
                            console.log('SalaryForm onCancel 被触发');

                            // 先关闭模态框
                            setIsModalVisible(false);

                            // 使用 setTimeout 延迟清理状态，避免状态更新冲突
                            setTimeout(() => {
                                try {
                                    // 清理状态
                                    setCurrentEmployee(null);

                                    console.log('模态框已关闭，准备重新获取数据');

                                    // 延迟更长时间再重新获取数据，确保状态更新完成
                                    setTimeout(() => {
                                        console.log('取消后延迟重新获取数据...');
                                        fetchSalaryDataByMonth(currentYear, currentMonth);
                                    }, 500);
                                } catch (error) {
                                    console.error('清理状态失败:', error);
                                }
                            }, 300);
                        }}
                        onOk={handleFormSubmit}
                    />
                )}

                <SalaryDetail
                    visible={isDetailModalVisible}
                    employee={currentDetailEmployee}
                    onClose={() => {
                        setIsDetailModalVisible(false);
                        setCurrentDetailEmployee(null);
                    }}
                />

                <ExportDialog
                    isOpen={isExportDialogOpen}
                    onClose={() => setIsExportDialogOpen(false)}
                    onExport={handleExportWithFormat}
                    moduleType="薪资列表"
                />

                <SalaryPrecalculatorModal
                    visible={isPrecalculatorVisible}
                    onCancel={() => setIsPrecalculatorVisible(false)}
                />
            </div>
        </div>
    );
};

export default SalaryList;