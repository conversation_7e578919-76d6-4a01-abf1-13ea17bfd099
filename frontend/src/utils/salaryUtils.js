/**
 * 薪资计算工具函数
 * 统一处理整个系统中的薪资相关逻辑，避免重复和不一致
 */

/**
 * 映射学历到标准格式
 * @param {string} education - 学历
 * @param {Object} config - 薪资配置
 * @param {string} educationType - 学历类型 (全日制/非全日制)
 * @returns {string} 映射后的标准学历格式
 */
export const mapEducation = (education, config, educationType) => {
    // 输入验证
    if (!education || typeof education !== 'string') {
        console.warn('mapEducation: 无效的学历输入:', education);
        return '大专及以下'; // 默认返回最低学历
    }

    const edu = education.trim();

    // 如果是非全日制学历，直接返回大专及以下
    if (educationType === '非全日制') {
        console.log('非全日制学历，映射为大专及以下:', edu);
        return '大专及以下';
    }

    // 首先检查配置中的映射关系
    if (config?.SALARY_CONFIG?.EDUCATION_MAPPING && config.SALARY_CONFIG.EDUCATION_MAPPING[edu]) {
        console.log('使用配置映射:', edu, '->', config.SALARY_CONFIG.EDUCATION_MAPPING[edu]);
        return config.SALARY_CONFIG.EDUCATION_MAPPING[edu];
    }

    // 如果配置中的EDUCATION_COEFFICIENT直接包含该学历，直接返回
    if (config?.SALARY_CONFIG?.EDUCATION_COEFFICIENT && config.SALARY_CONFIG.EDUCATION_COEFFICIENT[edu]) {
        console.log('直接匹配学历系数配置:', edu);
        return edu;
    }

    // 进行模糊匹配
    if (edu.includes('专科') || edu.includes('大专')) {
        console.log('模糊匹配为大专及以下:', edu);
        return '大专及以下';
    }

    if (edu.includes('本科')) {
        if (edu.includes('985') || edu.includes('211')) {
            console.log('模糊匹配为本科（985/211院校）:', edu);
            return '本科（985/211院校）';
        } else {
            console.log('模糊匹配为本科（普通院校）:', edu);
            return '本科（普通院校）';
        }
    }

    if (edu.includes('硕士') || edu.includes('研究生')) {
        if (edu.includes('985') || edu.includes('211')) {
            console.log('模糊匹配为硕士（985/211院校）:', edu);
            return '硕士（985/211院校）';
        } else {
            console.log('模糊匹配为硕士（普通院校）:', edu);
            return '硕士（普通院校）';
        }
    }

    if (edu.includes('博士')) {
        if (edu.includes('985') || edu.includes('211')) {
            console.log('模糊匹配为博士（985/211院校）:', edu);
            return '博士（985/211院校）';
        } else {
            console.log('模糊匹配为博士（普通院校）:', edu);
            return '博士（普通院校）';
        }
    }

    // 如果无法匹配，返回默认值
    console.log('无法匹配学历，使用默认值:', edu);
    return '大专及以下';
};

/**
 * 获取学历系数
 * @param {string} education - 学历
 * @param {Object} config - 薪资配置
 * @returns {number} 学历系数
 */
export const getEducationCoefficient = (education, config) => {
    if (!education || !config?.SALARY_CONFIG?.EDUCATION_COEFFICIENT) {
        return 1.0;
    }
    
    // 直接使用学历获取系数
    return config.SALARY_CONFIG.EDUCATION_COEFFICIENT[education] || 1.0;
};

/**
 * 计算学历调整值
 * @param {string} education - 学历
 * @param {Object} config - 薪资配置
 * @param {number} baseSalary - 基本工资
 * @returns {number} 学历调整值
 */
export const calculateEducationAdjustment = (education, config, baseSalary) => {
    const coefficient = getEducationCoefficient(education, config);
    return Math.round(baseSalary * (coefficient - 1));
};

/**
 * 获取语言系数
 * @param {string} languageLevel - 语言等级
 * @param {Object} config - 薪资配置
 * @returns {number} 语言系数
 */
export const getLanguageCoefficient = (languageLevel, config) => {
    if (!languageLevel || !config?.SALARY_CONFIG?.LANGUAGE_COEFFICIENT) {
        return 1.0;
    }
    
    // 标准化语言级别
    const normalizedLevel = normalizeLanguageLevel(languageLevel);
    console.log('获取语言系数:', languageLevel, '->', normalizedLevel);
    
    return config.SALARY_CONFIG.LANGUAGE_COEFFICIENT[normalizedLevel] || 1.0;
};

/**
 * 标准化语言级别
 * @param {string} languageLevel - 原始语言级别
 * @returns {string} 标准化后的语言级别
 */
export const normalizeLanguageLevel = (languageLevel) => {
    if (!languageLevel || typeof languageLevel !== 'string') {
        return '无';
    }
    
    const level = languageLevel.trim();
    
    // 简单映射
    const languageMap = {
        '无': '无',
        '基础': '基础',
        '熟练': '熟练',
        '精通': '精通',
        '未知': '无'
    };
    
    if (languageMap[level]) {
        return languageMap[level];
    }
    
    // 关键词匹配
    if (level.includes('精通') || level.includes('流利')) {
        return '精通';
    }
    
    if (level.includes('熟练') || level.includes('良好')) {
        return '熟练';
    }
    
    if (level.includes('基础') || level.includes('一般')) {
        return '基础';
    }
    
    // 默认返回无
    return '无';
};

/**
 * 计算语言调整值
 * @param {string} languageLevel - 语言等级
 * @param {Object} config - 薪资配置
 * @param {number} baseSalary - 基本工资
 * @returns {number} 语言调整值
 */
export const calculateLanguageAdjustment = (languageLevel, config, baseSalary) => {
    const coefficient = getLanguageCoefficient(languageLevel, config);
    return Math.round(baseSalary * (coefficient - 1));
};

/**
 * 格式化货币显示
 * @param {number} value - 货币值
 * @param {boolean} isBaseSalary - 是否为基本工资（整数显示）
 * @returns {string} 格式化后的货币字符串
 */
export const formatCurrency = (value, isBaseSalary = false) => {
    if (!value && value !== 0) return '-';
    
    if (isBaseSalary) {
        const intValue = Math.floor(value);
        return new Intl.NumberFormat('zh-CN', {
            style: 'currency',
            currency: 'CNY',
            minimumFractionDigits: 0,
            maximumFractionDigits: 0
        }).format(intValue);
    } else {
        const roundedValue = Math.round((value + Number.EPSILON) * 100) / 100;
        return new Intl.NumberFormat('zh-CN', {
            style: 'currency',
            currency: 'CNY',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(roundedValue);
    }
};

/**
 * 安全获取数值，避免|| 0导致的负值问题
 * @param {*} value - 原始值
 * @param {number} defaultValue - 默认值
 * @returns {number} 安全的数值
 */
export const safeNumber = (value, defaultValue = 0) => {
    return value !== undefined && value !== null ? Number(value) : defaultValue;
};

/**
 * 检查薪资是否被重置
 * @param {Object} employeeData - 员工薪资数据
 * @returns {boolean} 是否被重置
 */
export const isResetSalary = (employeeData) => {
    if (!employeeData) return true;
    
    return (
        safeNumber(employeeData.adjustedBaseSalary) === 0 &&
        safeNumber(employeeData.educationAdjustment) === 0 &&
        safeNumber(employeeData.languageAdjustment) === 0 &&
        safeNumber(employeeData.positionSalary) === 0 &&
        safeNumber(employeeData.adminSalary) === 0 &&
        safeNumber(employeeData.performanceBonus) === 0
    );
};

/**
 * 检查员工是否在试用期
 * @param {Object} employeeData - 员工数据
 * @returns {boolean} 是否在试用期
 */
export const isProbationPeriod = (employeeData) => {
    if (!employeeData) return false;

    // 如果工作类型是试用，直接返回true
    if (employeeData.workType === '试用') return true;

    // 如果有isProbation字段且为true，返回true
    if (employeeData.isProbation === true) return true;

    // 如果有试用期结束日期，检查当前日期是否在试用期内
    if (employeeData.probationEndDate) {
        const today = new Date();
        const probationEndDate = new Date(employeeData.probationEndDate);
        return today <= probationEndDate;
    }

    return false;
};

/**
 * 计算完整的基本工资（包括学历和语言调整）
 * @param {string} education - 学历
 * @param {string} languageLevel - 语言等级
 * @param {Object} config - 薪资配置
 * @param {boolean} isProbation - 是否试用期
 * @returns {Object} 基本工资计算结果
 */
export const calculateBaseSalary = (education, languageLevel, config, isProbation = false) => {
    const baseSalary = Number(config?.SALARY_CONFIG?.BASE_SALARY) || 3500;
    
    // 计算学历调整
    const educationCoefficient = getEducationCoefficient(education, config);
    const educationAdjustment = calculateEducationAdjustment(education, config, baseSalary);
    
    // 计算语言调整
    const languageCoefficient = getLanguageCoefficient(languageLevel, config);
    const languageAdjustment = calculateLanguageAdjustment(languageLevel, config, baseSalary);
    
    // 计算调整后基本工资
    let adjustedBaseSalary = baseSalary + educationAdjustment + languageAdjustment;
    adjustedBaseSalary = Math.round((adjustedBaseSalary + Number.EPSILON) * 100) / 100;

    return {
        originalBaseSalary: baseSalary,
        educationCoefficient,
        educationAdjustment,
        languageCoefficient,
        languageAdjustment,
        adjustedBaseSalary
    };
};

/**
 * 验证薪资数据一致性
 * @param {Object} employeeData - 员工薪资数据
 * @param {Object} config - 薪资配置
 * @returns {Object} 验证结果和修正建议
 */
export const validateSalaryConsistency = (employeeData, config) => {
    if (!employeeData || !config) {
        return { isConsistent: false, needsCorrection: false, correctedData: null };
    }

    // 如果是重置薪资，跳过验证
    if (isResetSalary(employeeData)) {
        return { isConsistent: true, needsCorrection: false, correctedData: null };
    }

    const { education, languageLevel } = employeeData;
    const isProbation = isProbationPeriod(employeeData);
    
    // 计算期望值
    const expected = calculateBaseSalary(education, languageLevel, config);

    // 获取实际值
    const actual = {
        adjustedBaseSalary: safeNumber(employeeData.adjustedBaseSalary),
        educationAdjustment: safeNumber(employeeData.educationAdjustment),
        languageAdjustment: safeNumber(employeeData.languageAdjustment),
        educationCoefficient: safeNumber(employeeData.educationCoefficient, 1.0),
        languageCoefficient: safeNumber(employeeData.languageCoefficient, 1.0)
    };

    // 计算差异
    const baseSalaryDifference = Math.abs(expected.adjustedBaseSalary - actual.adjustedBaseSalary);
    const educationDifference = Math.abs(expected.educationAdjustment - actual.educationAdjustment);
    const languageDifference = Math.abs(expected.languageAdjustment - actual.languageAdjustment);
    
    // 判断是否需要修正
    const needsCorrection = (
        baseSalaryDifference > 100 || // 基本工资差异太大
        (expected.educationAdjustment !== 0 && actual.educationAdjustment === 0) || // 应该有学历调整但实际为0
        (expected.languageAdjustment !== 0 && actual.languageAdjustment === 0) || // 应该有语言调整但实际为0
        (actual.adjustedBaseSalary === expected.originalBaseSalary && (expected.educationAdjustment !== 0 || expected.languageAdjustment !== 0)) // 基本工资是默认值但应该有调整
    );
    
    return {
        isConsistent: !needsCorrection,
        needsCorrection,
        differences: { baseSalaryDifference, educationDifference, languageDifference },
        expected,
        actual,
        correctedData: needsCorrection ? expected : null
    };
};

/**
 * 处理员工薪资数据，确保数据一致性
 * @param {Object} employeeData - 员工数据
 * @param {Object} config - 薪资配置
 * @returns {Object} 处理后的员工数据
 */
export const processSalaryData = (employeeData, config) => {
    if (!employeeData || !config) return employeeData;

    const processedData = { ...employeeData };
    
    // 确定学历和语言等级（从员工数据中读取）
    const education = employeeData.education || employeeData.finalEducation || employeeData.firstEducation || '本科（普通院校）';
    let languageLevel = employeeData.languageLevel || '无';
    
    // 如果没有语言等级，根据听说读写能力判断
    if (!employeeData.languageLevel) {
        if (employeeData.languageListening === '精通' && employeeData.languageSpeaking === '精通') {
            languageLevel = '精通';
        } else if (employeeData.languageListening === '熟练' && employeeData.languageSpeaking === '熟练') {
            languageLevel = '熟练';
        } else if (employeeData.languageListening === '基础' || employeeData.languageSpeaking === '基础') {
            languageLevel = '基础';
        }
    }
    
    processedData.education = education;
    processedData.languageLevel = languageLevel;

    // 确保试用期状态正确设置
    processedData.isProbation = isProbationPeriod(employeeData);

    // 不进行任何自动修正，直接返回数据
    return processedData;
};

/**
 * 生成基本工资计算公式字符串
 * @param {Object} salaryData - 薪资数据
 * @param {Object} config - 薪资配置
 * @returns {string} 计算公式字符串
 */
export const generateSalaryFormula = (salaryData, config) => {
    if (!salaryData || !config) return '';

    if (isResetSalary(salaryData)) {
        return '已重置为 0（系统将使用默认系数：学历1.00，语言1.00）';
    }

    const baseSalary = Number(config?.SALARY_CONFIG?.BASE_SALARY) || 3500;
    const educationAdjustment = safeNumber(salaryData.educationAdjustment);
    const languageAdjustment = safeNumber(salaryData.languageAdjustment);
    const adjustedBaseSalary = safeNumber(salaryData.adjustedBaseSalary);
    const originalBaseSalary = safeNumber(salaryData.originalBaseSalary);
    const isProbation = isProbationPeriod(salaryData);

    const baseSalaryPart = formatCurrency(baseSalary).replace('¥', '');
    const educationPart = educationAdjustment >= 0
        ? ` + ${formatCurrency(educationAdjustment).replace('¥', '')}`
        : ` - ${formatCurrency(Math.abs(educationAdjustment)).replace('¥', '')}`;
    const languagePart = languageAdjustment >= 0
        ? ` + ${formatCurrency(languageAdjustment).replace('¥', '')}`
        : ` - ${formatCurrency(Math.abs(languageAdjustment)).replace('¥', '')}`;

    // 试用期员工不显示"× 80%"，直接显示最终结果
    const finalValue = adjustedBaseSalary;
    const finalPart = ` = ${formatCurrency(finalValue).replace('¥', '')}`;

    return baseSalaryPart + educationPart + languagePart + finalPart;
};

/**
 * 根据绩效等级获取对应的名称
 * @param {string} level - 绩效等级
 * @returns {string} 绩效等级名称
 */
export const getPerformanceLevelName = (level) => {
    const names = {
        'A': '优秀',
        'B+': '良好',
        'B': '合格',
        'C': '待改进',
        'D': '不合格'
    };
    return names[level] || '';
};

/**
 * 根据绩效等级获取对应的系数
 * @param {string} performanceLevel - 绩效等级
 * @param {Object} config - 薪资配置
 * @returns {number} 绩效系数
 */
export const getPerformanceCoefficient = (performanceLevel, config) => {
    const performanceCoefficients = config?.SALARY_CONFIG?.PERFORMANCE_LEVELS || {
        'A': 1.5,
        'B+': 1.3,
        'B': 1.1,
        'C': 0.8,
        'D': 0.5
    };
    return performanceCoefficients[performanceLevel] || 1.0;
};

/**
 * 计算总津贴金额
 * @param {Object} employeeData - 员工数据
 * @returns {number} 总津贴金额
 */
export const calculateTotalAllowance = (employeeData) => {
    if (!employeeData) return 0;
    
    // 如果基本工资为0，则所有津贴也应为0
    if (safeNumber(employeeData.adjustedBaseSalary) === 0) {
        return 0;
    }
    
    const adminSalary = safeNumber(employeeData.adminSalary);
    const mealAllowance = safeNumber(employeeData.calculationResult?.mealAllowance);
    const communicationAllowance = safeNumber(employeeData.calculationResult?.communicationAllowance);
    const specialAllowance = safeNumber(employeeData.specialAllowance?.amount);
    
    return adminSalary + mealAllowance + communicationAllowance + specialAllowance;
};

/**
 * 创建空的薪资结果对象
 * @param {string} employeeId - 员工ID
 * @param {string} name - 员工姓名
 * @param {Object} config - 薪资配置
 * @param {boolean} isProbation - 是否试用期
 * @returns {Object} 空的薪资结果对象
 */
export const createEmptySalaryResult = (employeeId, name, config, isProbation = false) => {
    return {
        employeeId,
        name,
        adjustedBaseSalary: 0,
        originalBaseSalary: 0,
        educationAdjustment: 0,
        languageAdjustment: 0,
        educationCoefficient: 1.0,
        languageCoefficient: 1.0,
        positionSalary: 0,
        originalPositionSalary: 0,
        adminSalary: 0,
        originalAdminSalary: 0,
        performanceBonus: 0,
        originalPerformanceBonus: 0,
        performanceLevel: 'B',
        performanceCoefficient: 1.0,
        isProbation,
        probationFactor: isProbation ? 0.8 : 1.0,
        calculationResult: {
            mealAllowance: 0,
            communicationAllowance: 0,
            totalMonthlySalary: 0,
            socialInsurance: 0,
            tax: 0,
            netSalary: 0,
            absenceDeduction: 0,
            attendanceAdjustment: 0,
            isProbation,
            probationFactor: isProbation ? 0.8 : 1.0
        }
    };
};

/**
 * 获取默认的薪资数据
 * @param {Object} employee - 员工基本信息
 * @returns {Object} 默认的薪资数据
 */
export const getDefaultSalaryData = (employee = {}) => {
    // 根据岗位类型设置默认岗位等级
    let defaultPositionLevel = 'D1';
    if (employee.positionType === '技术') {
        defaultPositionLevel = 'A1';
    } else if (employee.positionType === '高管') {
        defaultPositionLevel = 'B1';
    } else if (employee.positionType === '支持') {
        defaultPositionLevel = 'C1';
    } else if (employee.positionType === '其他') {
        defaultPositionLevel = 'D1';
    }
    
    return {
        // 保留员工基本信息
        employeeId: employee.employeeId,
        name: employee.name,
        department: employee.department,
        subDepartment: employee.subDepartment,
        education: employee.education,
        languageLevel: employee.languageLevel,
        administrativeLevel: employee.administrativeLevel || '无',
        positionType: employee.positionType || '其他',
        
        // 工作状态
        isProbation: employee.isProbation || false,
        workType: employee.workType || '全职',
        probationEndDate: employee.probationEndDate || '',
        
        // 默认的薪资计算参数
        positionLevel: defaultPositionLevel,
        performanceLevel: 'B',
        performanceCoefficient: 1.0,
        actualAttendance: 22,
        specialAllowance: { remark: '', amount: 0 },
        specialDeduction: { amount: 0 },
        
        // 默认的薪资计算结果（全部为0）
        adjustedBaseSalary: 0,
        originalBaseSalary: 0,
        educationCoefficient: 1.0,
        educationAdjustment: 0,
        languageCoefficient: 1.0,
        languageAdjustment: 0,
        positionSalary: 0,
        originalPositionSalary: 0,
        adminSalary: 0,
        originalAdminSalary: 0,
        performanceBonus: 0,
        originalPerformanceBonus: 0,
        probationFactor: employee.isProbation ? 0.8 : 1.0,
        
        // 默认的计算结果
        calculationResult: {
            mealAllowance: 0,
            communicationAllowance: 0,
            totalMonthlySalary: 0,
            socialInsurance: 0,
            tax: 0,
            netSalary: 0,
            absenceDeduction: 0,
            attendanceAdjustment: 0,
            isProbation: employee.isProbation || false,
            probationFactor: employee.isProbation ? 0.8 : 1.0
        }
    };
};

/**
 * 获取岗位类型
 * @param {Object} employee - 员工数据
 * @param {Object} config - 薪资配置
 * @returns {string} 岗位类型
 */
export function getPositionType(employee, config) {
    console.log('[DEBUG] getPositionType 输入:', {
        employee,
        config: config?.SALARY_CONFIG?.POSITION_TYPE_MAP
    });

    // 如果员工已经有岗位类型，直接返回
    if (employee.positionType) {
        console.log('[DEBUG] 使用已有岗位类型:', employee.positionType);
        return employee.positionType;
    }

    // 如果没有配置，使用默认映射
    const defaultPositionTypeMap = {
        // 技术类岗位
        '技术工程师': '技术',
        '项目经理': '技术',
        '项目代表': '技术',
        '项目助理': '技术',

        // 支持类岗位
        '项目管理': '支持',
        '业务经理': '支持',
        '项目管理专员': '支持',
        '采购工程师': '支持',
        '品控工程师': '支持',
        '采购助理': '支持',

        // 其他类岗位
        '外贸专员': '其他',
        '出纳': '其他',
        '会计': '其他',
        '出纳/会计': '其他',
        '行政助理': '其他'
    };

    const adminLevels = ['总经理', '副总经理', '总监/总工', '副总监/副总工'];
    if (adminLevels.includes(employee.administrativeLevel)) {
        console.log('[DEBUG] 判断为高管:', employee.administrativeLevel);
        return '高管';
    }

    // 使用配置中的映射或默认映射
    const positionTypeMap = config?.POSITION_TYPE_MAP || config?.SALARY_CONFIG?.POSITION_TYPE_MAP || defaultPositionTypeMap;
    const type = positionTypeMap[employee.position];
    
    if (type) {
        console.log('[DEBUG] 根据岗位名称判断:', {
            position: employee.position,
            type: type
        });
        return type;
    }

    console.log('[DEBUG] 默认为其他:', {
        position: employee.position,
        administrativeLevel: employee.administrativeLevel
    });
    return '其他';
}

// 默认导出
export default {
    formatCurrency,
    safeNumber,
    isResetSalary,
    isProbationPeriod,
    calculateBaseSalary,
    validateSalaryConsistency,
    processSalaryData,
    generateSalaryFormula,
    getPerformanceLevelName,
    getPerformanceCoefficient,
    calculateTotalAllowance,
    createEmptySalaryResult,
    getDefaultSalaryData,
    getPositionType
};