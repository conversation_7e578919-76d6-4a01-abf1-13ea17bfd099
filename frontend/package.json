{"name": "hr-management-system", "version": "1.3.0", "private": true, "description": "HR管理系统 - 一个基于Electron和React的人力资源管理系统", "author": {"name": "Your Name", "email": "<EMAIL>"}, "main": "public/electron.js", "homepage": "./", "dependencies": {"@ant-design/icons": "^4.8.0", "@ant-design/plots": "^1.2.5", "@reduxjs/toolkit": "^2.5.1", "antd": "^5.0.0", "chinese-to-pinyin": "^1.3.1", "html2canvas": "^1.4.1", "jspdf": "^2.5.2", "jspdf-autotable": "^3.8.4", "math-intrinsics": "^1.1.0", "node-fetch": "^2.7.0", "pdfmake": "^0.2.18", "pinyin": "^4.0.0-alpha.2", "react": "^18.2.0", "react-datepicker": "^8.3.0", "react-dom": "^18.2.0", "react-draggable": "^4.4.6", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router-dom": "^6.29.0", "react-scripts": "^5.0.1", "segmentit": "^2.0.3", "xlsx": "^0.18.5"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "electron-dev": "concurrently \"BROWSER=none npm start\" \"wait-on http://localhost:3000 && npx electron .\"", "convert-icon": "node scripts/convert-icon.js", "electron-pack": "npm run convert-icon && npm run build && electron-builder build --mac"}, "build": {"appId": "com.yourcompany.hrmanagement", "productName": "HR管理系统", "files": ["build/**/*", "node_modules/**/*"], "directories": {"buildResources": "./assets", "output": "dist"}, "extraResources": [{"from": "../backend", "to": "backend", "filter": ["**/*"]}], "asar": true, "forceCodeSigning": false, "mac": {"category": "public.app-category.business", "icon": "./assets/icon.icns", "target": ["dmg"], "artifactName": "${productName}-${version}.${ext}", "darkModeSupport": true, "hardenedRuntime": false, "extendInfo": {"NSDocumentsFolderUsageDescription": "应用需要访问文档文件夹", "NSDesktopFolderUsageDescription": "应用需要访问桌面文件夹"}}}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "concurrently": "^6.5.1", "electron": "25.9.8", "electron-builder": "^24.6.4", "electron-is-dev": "^2.0.0", "patch-package": "^8.0.0", "png2icons": "^2.0.1", "sharp": "^0.33.5", "wait-on": "^6.0.1"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5006"}